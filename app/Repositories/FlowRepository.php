<?php

namespace App\Repositories;

use App\Domains\ChatBot\Flow as FlowDomain;
use App\Domains\Filters\FlowFilters;
use App\Domains\Filters\OrderBy;
use App\Factories\ChatBot\FlowFactory;
use App\Models\Flow;
use EloquentBuilder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FlowRepository
{
    private FlowFactory $flowFactory;

    // Cache configuration
    private const CACHE_TTL = 3600; // 1 hour
    private const CACHE_PREFIX = 'flow:';

    public function __construct(FlowFactory $flowFactory){
        $this->flowFactory = $flowFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(FlowFilters $filters, OrderBy $orderBy) : array {
        $flows = [];

        $models = EloquentBuilder::to(Flow::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $flows[] = $this->flowFactory->buildFromModel($model);
        }

        return [
            'data' => $flows,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, FlowFilters $filters, OrderBy $orderBy) : array {
        $flows = [];

        $models = EloquentBuilder::to(Flow::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $flows[] = $this->flowFactory->buildFromModel($model);
        }

        return [
            'data' => $flows,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, FlowFilters $filters): int {
        return EloquentBuilder::to(Flow::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, FlowFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Flow::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    /**
     * Store new flow with optimized performance and cache management
     */
    public function store(FlowDomain $flow) : FlowDomain {
        $startTime = microtime(true);

        try {
            // Validate required fields
            if (!$flow->organization_id) {
                throw new \InvalidArgumentException('Organization ID is required for flow creation');
            }

            if (empty($flow->name)) {
                throw new \InvalidArgumentException('Flow name is required');
            }

            // Create flow with optimized query
            $savedFlow = Flow::create($flow->toStoreArray());
            $flow->id = $savedFlow->id;

            // Invalidate organization flows cache
            $this->invalidateOrganizationCache($flow->organization_id);

            // Log performance metrics
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            Log::info('FlowRepository::store completed', [
                'flow_id' => $flow->id,
                'organization_id' => $flow->organization_id,
                'execution_time_ms' => $executionTime
            ]);

            return $flow;

        } catch (\Throwable $exception) {
            Log::error('FlowRepository::store failed', [
                'organization_id' => $flow->organization_id,
                'flow_name' => $flow->name,
                'error' => $exception->getMessage()
            ]);
            throw $exception;
        }
    }

    /**
     * Update existing flow with optimized performance and cache management
     */
    public function update(FlowDomain $flow, int $organization_id) : FlowDomain {
        $startTime = microtime(true);

        try {
            // Validate required fields
            if (!$flow->id) {
                throw new \InvalidArgumentException('Flow ID is required for update');
            }

            if (!$organization_id) {
                throw new \InvalidArgumentException('Organization ID is required for flow update');
            }

            // Update with optimized query and organization validation
            $affectedRows = Flow::where('id', $flow->id)
                ->where('organization_id', $organization_id)
                ->update($flow->toUpdateArray());

            if ($affectedRows === 0) {
                throw new \RuntimeException("Flow {$flow->id} not found or does not belong to organization {$organization_id}");
            }

            // Invalidate caches
            $this->invalidateFlowCache($flow->id);
            $this->invalidateOrganizationCache($organization_id);

            // Log performance metrics
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            Log::info('FlowRepository::update completed', [
                'flow_id' => $flow->id,
                'organization_id' => $organization_id,
                'execution_time_ms' => $executionTime
            ]);

            return $flow;

        } catch (\Throwable $exception) {
            Log::error('FlowRepository::update failed', [
                'flow_id' => $flow->id,
                'organization_id' => $organization_id,
                'error' => $exception->getMessage()
            ]);
            throw $exception;
        }
    }

    /**
     * Save flow (create or update) with optimized transaction handling
     */
    public function save(FlowDomain $flow, int $organization_id) : FlowDomain {
        return DB::transaction(function () use ($flow, $organization_id) {
            if ($flow->id) {
                return $this->update($flow, $organization_id);
            }
            return $this->store($flow);
        });
    }

    /**
     * Find flow by ID with caching
     */
    public function findById(int $flowId, int $organizationId): ?FlowDomain
    {
        $cacheKey = self::CACHE_PREFIX . "id:{$flowId}:org:{$organizationId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($flowId, $organizationId) {
            $model = Flow::where('id', $flowId)
                ->where('organization_id', $organizationId)
                ->first();

            return $model ? $this->flowFactory->buildFromModel($model) : null;
        });
    }

    public function fetchById(int $id) : FlowDomain {
        return $this->flowFactory->buildFromModel(
            Flow::findOrFail($id)
        );
    }

    public function fetchByBot(string $bot) : FlowDomain {
        return $this->flowFactory->buildFromModel(
            Flow::where('bot', $bot)->first()
        );
    }

    public function fetchDefaultFlow(int $organization_id) : ?FlowDomain {
        return $this->flowFactory->buildFromModel(
            Flow::where('organization_id', $organization_id)
                ->where('is_default_flow', true)
                ->first()
        );
    }

    public function delete(FlowDomain $flow) : bool {
        $result = Flow::find($flow->id)->delete();

        if ($result && $flow->organization_id) {
            $this->invalidateFlowCache($flow->id);
            $this->invalidateOrganizationCache($flow->organization_id);
        }

        return $result;
    }

    /**
     * Find default flow for organization
     */
    public function findDefaultFlowForOrganization(int $organizationId): ?FlowDomain
    {
        $model = Flow::where('organization_id', $organizationId)
            ->where('is_default_flow', true)
            ->with('steps')
            ->first();

        return $model ? $this->flowFactory->buildFromModel($model) : null;
    }

    /**
     * Bulk create flows with optimized performance
     */
    public function bulkStore(array $flows, int $organizationId): array
    {
        $startTime = microtime(true);

        return DB::transaction(function () use ($flows, $organizationId, $startTime) {
            $flowData = [];
            $createdFlows = [];

            // Prepare bulk insert data
            foreach ($flows as $flow) {
                if (!$flow instanceof FlowDomain) {
                    throw new \InvalidArgumentException('All items must be FlowDomain instances');
                }

                $flow->organization_id = $organizationId;
                $flowData[] = $flow->toStoreArray();
            }

            // Bulk insert
            Flow::insert($flowData);

            // Get created flows (note: bulk insert doesn't return IDs in Laravel)
            $createdModels = Flow::where('organization_id', $organizationId)
                ->whereIn('name', array_column($flowData, 'name'))
                ->orderBy('created_at', 'desc')
                ->limit(count($flowData))
                ->get();

            // Build domain objects
            foreach ($createdModels as $model) {
                $createdFlows[] = $this->flowFactory->buildFromModel($model);
            }

            // Invalidate cache
            $this->invalidateOrganizationCache($organizationId);

            // Log performance
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            Log::info('FlowRepository::bulkStore completed', [
                'organization_id' => $organizationId,
                'flows_count' => count($flows),
                'execution_time_ms' => $executionTime
            ]);

            return $createdFlows;
        });
    }

    /**
     * Invalidate flow-specific cache
     */
    private function invalidateFlowCache(int $flowId): void
    {
        $pattern = self::CACHE_PREFIX . "id:{$flowId}:*";
        $this->clearCacheByPattern($pattern);
    }

    /**
     * Invalidate organization-specific cache
     */
    private function invalidateOrganizationCache(int $organizationId): void
    {
        $patterns = [
            self::CACHE_PREFIX . "*:org:{$organizationId}",
            self::CACHE_PREFIX . "org:{$organizationId}:*"
        ];

        foreach ($patterns as $pattern) {
            $this->clearCacheByPattern($pattern);
        }
    }

    /**
     * Clear cache by pattern (simplified implementation)
     */
    private function clearCacheByPattern(string $pattern): void
    {
        // Note: This is a simplified implementation
        // In production, consider using Redis with SCAN or tagged cache
        try {
            Cache::flush(); // Simplified - flushes all cache
        } catch (\Exception $e) {
            Log::warning('Cache invalidation failed', [
                'pattern' => $pattern,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get flow statistics for organization
     */
    public function getOrganizationStats(int $organizationId): array
    {
        $cacheKey = self::CACHE_PREFIX . "stats:org:{$organizationId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($organizationId) {
            return [
                'total_flows' => Flow::where('organization_id', $organizationId)->count(),
                'active_flows' => Flow::where('organization_id', $organizationId)
                    ->where('status', 'active')->count(),
                'draft_flows' => Flow::where('organization_id', $organizationId)
                    ->where('status', 'draft')->count(),
                'default_flows' => Flow::where('organization_id', $organizationId)
                    ->where('is_default_flow', true)->count(),
            ];
        });
    }
}
