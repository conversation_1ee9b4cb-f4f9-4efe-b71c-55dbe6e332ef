<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Flow;
use App\Enums\ChatBot\FlowStatus;
use App\Http\Requests\Flow\StoreRequest;
use App\Http\Requests\Flow\UpdateRequest;
use App\Models\Flow as FlowModel;

class FlowFactory
{

    public StepFactory $stepFactory;

    public function __construct(StepFactory $stepFactory) {
        $this->stepFactory = $stepFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : Flow {
        return new Flow(
            null,
            $request->organization_id ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->steps_count ?? null,
            $request->json ?? null,
            $request->is_default_flow ?? false,
            $request->inactivity_minutes ?? 60,
            $request->ending_conversation_message ?? null,
            $request->version ?? '1.0',
            isset($request->status) ? FlowStatus::from($request->status) : FlowStatus::DRAFT,
            $request->variables ?? null,
        );
    }

    /**
     * Build Flow domain from SaveFullFlow request with optimized data processing
     *
     * @param array $flow Flow data array
     * @param string $json Complete JSON representation
     * @param int $steps_count Number of steps
     * @param int $organization_id Organization ID
     * @param int|null $id Flow ID for updates
     * @return Flow
     */
    public function buildFromSaveFullFlow(
        array $flow,
        string $json,
        ?int $steps_count,
        ?int $organization_id,
        ?int $id
    ) : Flow {
        // Validate required fields
        if (empty($flow['name'])) {
            throw new \InvalidArgumentException('Flow name is required');
        }

        if (!$organization_id) {
            throw new \InvalidArgumentException('Organization ID is required');
        }

        // Process status with validation
        $status = FlowStatus::DRAFT; // Default
        if (isset($flow['status'])) {
            try {
                $status = FlowStatus::from($flow['status']);
            } catch (\ValueError $e) {
                throw new \InvalidArgumentException("Invalid flow status: {$flow['status']}");
            }
        }

        // Process variables with validation
        $variables = null;
        if (isset($flow['variables'])) {
            if (is_array($flow['variables'])) {
                $variables = $flow['variables'];
            } else {
                throw new \InvalidArgumentException('Flow variables must be an array');
            }
        }

        // Validate inactivity_minutes range
        $inactivityMinutes = $flow['inactivity_minutes'] ?? 60;
        if ($inactivityMinutes < 1 || $inactivityMinutes > 1440) {
            throw new \InvalidArgumentException('Inactivity minutes must be between 1 and 1440');
        }

        return new Flow(
            $id,
            $organization_id,
            trim($flow['name']),
            isset($flow['description']) ? trim($flow['description']) : null,
            $steps_count,
            $json,
            (bool) ($flow['is_default_flow'] ?? false),
            $inactivityMinutes,
            isset($flow['ending_conversation_message']) ? trim($flow['ending_conversation_message']) : null,
            $flow['version'] ?? '1.0',
            $status,
            $variables,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Flow {
        return new Flow(
            null,
            $request->organization_id ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->steps_count ?? null,
            $request->json ?? null,
            $request->is_default_flow ?? false,
            $request->inactivity_minutes ?? 60,
            $request->ending_conversation_message ?? null,
            $request->version ?? '1.0',
            isset($request->status) ? FlowStatus::from($request->status) : FlowStatus::DRAFT,
            $request->variables ?? null,
        );
    }

    public function buildFromModel(?FlowModel $flow, bool $with_steps = true) : ?Flow {
        if (!$flow){ return null; }

        return new Flow(
            $flow->id ?? null,
            $flow->organization_id ?? null,
            $flow->name ?? null,
            $flow->description ?? null,
            $flow->steps_count ?? null,
            $flow->json ?? null,
            $flow->is_default_flow ?? false,
            $flow->inactivity_minutes ?? 60,
            $flow->ending_conversation_message ?? null,
            $flow->version ?? '1.0',
            isset($flow->status) ? FlowStatus::from($flow->status) : FlowStatus::DRAFT,
            $flow->variables ? json_decode($flow->variables, true) : null,
            $flow->created_at ?? null,
            $flow->updated_at ?? null,
            ($with_steps) ? $this->stepFactory->buildFromModels($flow->steps ?? null, false, true) : null
        );
    }
}
