<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\ListRow;
use App\Models\ListRow as ListRowModel;
use Illuminate\Support\Collection;

class ListRowFactory
{
    /**
     * Build ListRow from Eloquent model
     */
    public function buildFromModel(?ListRowModel $model): ?ListRow
    {
        if (!$model) {
            return null;
        }

        return new ListRow(
            id: $model->id,
            list_section_id: $model->list_section_id,
            row_id: $model->row_id,
            title: $model->title,
            description: $model->description,
            created_at: $model->created_at,
            updated_at: $model->updated_at
        );
    }

    /**
     * Build multiple ListRows from collection
     */
    public function buildFromModels(?Collection $models): ?array
    {
        if (!$models) {
            return null;
        }

        $domains = [];

        foreach ($models as $model) {
            $domain = $this->buildFromModel($model);
            if ($domain) {
                $domains[] = $domain;
            }
        }

        return $domains;
    }

    /**
     * Build ListRow from array data
     */
    public function buildFromArray(array $data): ListRow
    {
        return new ListRow(
            id: $data['id'] ?? null,
            list_section_id: $data['list_section_id'] ?? null,
            row_id: $data['row_id'] ?? null,
            title: $data['title'] ?? null,
            description: $data['description'] ?? null,
            created_at: isset($data['created_at']) ? \Carbon\Carbon::parse($data['created_at']) : null,
            updated_at: isset($data['updated_at']) ? \Carbon\Carbon::parse($data['updated_at']) : null
        );
    }

    /**
     * Build ListRow with validation
     */
    public function buildValidatedRow(
        string $title,
        ?string $description = null,
        ?string $row_id = null,
        ?int $list_section_id = null
    ): ListRow {
        if (strlen($title) > 24) {
            throw new \InvalidArgumentException('Row title cannot exceed 24 characters');
        }

        if ($description && strlen($description) > 72) {
            throw new \InvalidArgumentException('Row description cannot exceed 72 characters');
        }

        if ($row_id && strlen($row_id) > 200) {
            throw new \InvalidArgumentException('Row ID cannot exceed 200 characters');
        }

        return new ListRow(
            id: null,
            list_section_id: $list_section_id,
            row_id: $row_id,
            title: $title,
            description: $description
        );
    }

    /**
     * Build multiple validated rows from array
     */
    public function buildValidatedRows(array $rowsData, ?int $list_section_id = null): array
    {
        $rows = [];

        foreach ($rowsData as $index => $rowData) {
            if (is_string($rowData)) {
                $rows[] = $this->buildValidatedRow(
                    title: $rowData,
                    row_id: 'row_' . ($index + 1),
                    list_section_id: $list_section_id
                );
            } elseif (is_array($rowData)) {
                $rows[] = $this->buildValidatedRow(
                    title: $rowData['title'] ?? '',
                    description: $rowData['description'] ?? null,
                    row_id: $rowData['row_id'] ?? 'row_' . ($index + 1),
                    list_section_id: $list_section_id
                );
            }
        }

        return $rows;
    }
}
