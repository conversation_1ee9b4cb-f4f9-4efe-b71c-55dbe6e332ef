<?php

namespace App\Domains\ChatBot;

use Carbon\Carbon;
use InvalidArgumentException;

class ListRow
{
    public ?int $id;
    public ?int $list_section_id;
    public ?string $row_id; // Unique identifier for the row (used in WhatsApp API)
    public ?string $title;
    public ?string $description;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public function __construct(
        ?int $id = null,
        ?int $list_section_id = null,
        ?string $row_id = null,
        ?string $title = null,
        ?string $description = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null
    ) {
        $this->id = $id;
        $this->list_section_id = $list_section_id;
        $this->row_id = $row_id;
        $this->title = $title;
        $this->description = $description;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;

        $this->validateRow();
    }

    /**
     * Validate row according to WhatsApp API requirements
     */
    private function validateRow(): void
    {
        if ($this->title && strlen($this->title) > 24) {
            throw new InvalidArgumentException('Row title cannot exceed 24 characters');
        }

        if ($this->description && strlen($this->description) > 72) {
            throw new InvalidArgumentException('Row description cannot exceed 72 characters');
        }

        if ($this->row_id && strlen($this->row_id) > 200) {
            throw new InvalidArgumentException('Row ID cannot exceed 200 characters');
        }
    }

    /**
     * Generate unique row ID if not provided
     */
    public function generateRowId(): string
    {
        if ($this->row_id) {
            return $this->row_id;
        }

        // Generate based on title or use uniqid
        if ($this->title) {
            $this->row_id = 'row_' . preg_replace('/[^a-zA-Z0-9_]/', '_', strtolower($this->title)) . '_' . uniqid();
        } else {
            $this->row_id = 'row_' . uniqid();
        }

        // Ensure it doesn't exceed 200 characters
        if (strlen($this->row_id) > 200) {
            $this->row_id = substr($this->row_id, 0, 200);
        }

        return $this->row_id;
    }

    /**
     * Convert to WhatsApp API payload
     */
    public function toWhatsAppPayload(): array
    {
        $payload = [
            'id' => $this->generateRowId(),
        ];

        if ($this->title) {
            $payload['title'] = $this->title;
        }

        if ($this->description) {
            $payload['description'] = $this->description;
        }

        return $payload;
    }

    /**
     * Check if row is valid
     */
    public function isValid(): bool
    {
        try {
            $this->validateRow();
            return !empty($this->title); // Title is required for WhatsApp API
        } catch (InvalidArgumentException $e) {
            return false;
        }
    }

    /**
     * Get validation errors
     */
    public function getValidationErrors(): array
    {
        $errors = [];

        if (empty($this->title)) {
            $errors[] = 'Title is required';
        }

        try {
            $this->validateRow();
        } catch (InvalidArgumentException $e) {
            $errors[] = $e->getMessage();
        }

        return $errors;
    }

    /**
     * Set title with validation
     */
    public function setTitle(string $title): void
    {
        if (strlen($title) > 24) {
            throw new InvalidArgumentException('Row title cannot exceed 24 characters');
        }

        $this->title = $title;
    }

    /**
     * Set description with validation
     */
    public function setDescription(?string $description): void
    {
        if ($description && strlen($description) > 72) {
            throw new InvalidArgumentException('Row description cannot exceed 72 characters');
        }

        $this->description = $description;
    }

    /**
     * Set row ID with validation
     */
    public function setRowId(string $rowId): void
    {
        if (strlen($rowId) > 200) {
            throw new InvalidArgumentException('Row ID cannot exceed 200 characters');
        }

        $this->row_id = $rowId;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'list_section_id' => $this->list_section_id,
            'row_id' => $this->row_id,
            'title' => $this->title,
            'description' => $this->description,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'is_valid' => $this->isValid(),
            'title_length' => strlen($this->title ?? ''),
            'description_length' => strlen($this->description ?? ''),
            'row_id_length' => strlen($this->row_id ?? ''),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            'list_section_id' => $this->list_section_id,
            'row_id' => $this->generateRowId(),
            'title' => $this->title,
            'description' => $this->description,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'row_id' => $this->row_id,
            'title' => $this->title,
            'description' => $this->description,
        ];
    }
}
