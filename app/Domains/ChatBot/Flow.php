<?php

namespace App\Domains\ChatBot;

use App\Enums\ChatBot\FlowStatus;
use Carbon\Carbon;

class Flow
{
    public ?int $id;
    public ?int $organization_id;
    public ?string $name;
    public ?string $description;
    public ?int $steps_count;
    public ?string $json;
    public ?bool $is_default_flow;
    public ?int $inactivity_minutes;
    public ?string $ending_conversation_message;
    public ?string $version;
    public ?FlowStatus $status;
    public ?array $variables;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    /** @var Step[]|null $steps */
    public ?array $steps;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?string $name,
        ?string $description,
        ?int $steps_count,
        ?string $json,
        ?bool $is_default_flow = false,
        ?int $inactivity_minutes = 60,
        ?string $ending_conversation_message = null,
        ?string $version = '1.0',
        ?FlowStatus $status = FlowStatus::DRAFT,
        ?array $variables = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?array $steps = null
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->name = $name;
        $this->description = $description;
        $this->steps_count = $steps_count;
        $this->json = $json;
        $this->is_default_flow = $is_default_flow;
        $this->inactivity_minutes = $inactivity_minutes;
        $this->ending_conversation_message = $ending_conversation_message;
        $this->version = $version;
        $this->status = $status;
        $this->variables = $variables;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->steps = $steps;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "name" => $this->name,
            "description" => $this->description,
            "steps_count" => $this->steps_count,
            "json" => $this->json,
            "is_default_flow" => $this->is_default_flow,
            "inactivity_minutes" => $this->inactivity_minutes,
            "ending_conversation_message" => $this->ending_conversation_message,
            "version" => $this->version,
            "status" => $this->status?->value,
            "variables" => $this->variables,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "steps" => $this->steps,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "name" => $this->name,
            "description" => $this->description,
            "steps_count" => $this->steps_count,
            "json" => $this->json,
            "is_default_flow" => $this->is_default_flow,
            "inactivity_minutes" => $this->inactivity_minutes,
            "ending_conversation_message" => $this->ending_conversation_message,
            "version" => $this->version,
            "status" => $this->status?->value,
            "variables" => $this->variables ? json_encode($this->variables) : null,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "name" => $this->name,
            "description" => $this->description,
            "steps_count" => $this->steps_count,
            "json" => $this->json,
            "is_default_flow" => $this->is_default_flow,
            "inactivity_minutes" => $this->inactivity_minutes,
            "ending_conversation_message" => $this->ending_conversation_message,
            "version" => $this->version,
            "status" => $this->status?->value,
            "variables" => $this->variables ? json_encode($this->variables) : null,
        ];
    }

    /**
     * Reorder steps array by position in ascending order
     *
     * @return void
     */
    public function orderSteps(): void
    {
        if (!$this->steps) {
            return;
        }

        usort($this->steps, function (Step $a, Step $b) {
            return $a->position <=> $b->position;
        });
    }

    /**
     * Get the initial step from the step's array
     *
     * @return Step|null
     */
    public function getInitialStep(): ?Step
    {
        foreach (($this->steps ?? []) as $step) {
            if ($step->is_initial_step) {
                return $step;
            }
        }
        return null;
    }

    /**
     * Get the ending step from the step's array
     *
     * @return Step|null
     */
    public function getEndingStep(): ?Step
    {
        foreach (($this->steps ?? []) as $step) {
            if ($step->is_ending_step) {
                return $step;
            }
        }
        return null;
    }

    /**
     * Get a step by its position
     *
     * @param int $position
     * @return Step|null
     */
    public function getStepByPosition(int $position): ?Step
    {
        foreach (($this->steps ?? []) as $step) {
            if ($step->position === $position) {
                return $step;
            }
        }
        return null;
    }

    /**
     * Get timeout in minutes for flow inactivity
     *
     * @return int
     */
    public function getTimeoutMinutes(): int
    {
        return $this->inactivity_minutes ?? 60;
    }

    /**
     * Get ending conversation message
     *
     * @return string|null
     */
    public function getEndingMessage(): ?string
    {
        return $this->ending_conversation_message;
    }

    /**
     * Validate flow integrity
     * Checks for required fields, valid steps structure, and flow consistency
     *
     * @return array Array of validation errors (empty if valid)
     */
    public function validateFlowIntegrity(): array
    {
        $errors = [];

        // Check required fields
        if (empty($this->name)) {
            $errors[] = 'Flow name is required';
        }

        if (empty($this->organization_id)) {
            $errors[] = 'Organization ID is required';
        }

        if ($this->inactivity_minutes !== null && $this->inactivity_minutes < 1) {
            $errors[] = 'Inactivity minutes must be at least 1';
        }

        if (empty($this->version)) {
            $errors[] = 'Flow version is required';
        }

        if (!$this->status) {
            $errors[] = 'Flow status is required';
        }

        // Validate steps if present
        if ($this->steps !== null) {
            $hasInitialStep = false;
            $positions = [];

            foreach ($this->steps as $step) {
                if ($step->is_initial_step) {
                    if ($hasInitialStep) {
                        $errors[] = 'Flow can only have one initial step';
                    }
                    $hasInitialStep = true;
                }

                if (in_array($step->position, $positions)) {
                    $errors[] = "Duplicate step position: {$step->position}";
                }
                $positions[] = $step->position;
            }

            if (!$hasInitialStep && count($this->steps) > 0) {
                $errors[] = 'Flow must have an initial step';
            }

            // Validate steps count matches actual steps
            if ($this->steps_count !== null && $this->steps_count !== count($this->steps)) {
                $errors[] = 'Steps count does not match actual number of steps';
            }
        }

        // Validate variables structure if present
        if ($this->variables !== null && !is_array($this->variables)) {
            $errors[] = 'Variables must be an array';
        }

        return $errors;
    }
}
