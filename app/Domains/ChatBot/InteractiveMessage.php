<?php

namespace App\Domains\ChatBot;

use App\Enums\ChatBot\InteractiveType;
use App\Enums\ChatBot\WhatsAppButtonType;
use Carbon\Carbon;
use InvalidArgumentException;

class InteractiveMessage
{
    public ?int $id;
    public ?int $organization_id;
    public ?string $header;
    public ?string $body;
    public ?string $footer;
    public ?InteractiveType $type;
    public ?string $button_text; // For LIST type - text shown on the list button
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    /** @var Button[]|null $buttons */
    public ?array $buttons;

    /** @var ListSection[]|null $sections */
    public ?array $sections;

    public function __construct(
        ?int $id = null,
        ?int $organization_id = null,
        ?string $header = null,
        ?string $body = null,
        ?string $footer = null,
        ?InteractiveType $type = null,
        ?string $button_text = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?array $buttons = null,
        ?array $sections = null
    ) {
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->header = $header;
        $this->body = $body;
        $this->footer = $footer;
        $this->type = $type;
        $this->button_text = $button_text;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->buttons = $buttons;
        $this->sections = $sections;

        $this->validateLimits();
    }

    /**
     * Validate interactive message limits according to WhatsApp API
     */
    private function validateLimits(): void
    {
        if (!$this->type) {
            return;
        }

        switch ($this->type) {
            case InteractiveType::BUTTON:
                $this->validateButtonLimits();
                break;
            case InteractiveType::LIST:
                $this->validateListLimits();
                break;
        }
    }

    /**
     * Validate button message limits
     */
    private function validateButtonLimits(): void
    {
        if ($this->buttons && count($this->buttons) > 3) {
            throw new InvalidArgumentException('Button messages support maximum 3 buttons');
        }

        if ($this->buttons) {
            foreach ($this->buttons as $button) {
                if (!$button instanceof Button) {
                    throw new InvalidArgumentException('All buttons must be Button instances');
                }
                
                // Validate button type is compatible with interactive messages
                $buttonType = $button->getWhatsAppButtonType();
                if ($buttonType && !in_array($buttonType, InteractiveType::BUTTON->getCompatibleButtonTypes())) {
                    throw new InvalidArgumentException('Button type not compatible with interactive button messages');
                }
            }
        }
    }

    /**
     * Validate list message limits
     */
    private function validateListLimits(): void
    {
        if (!$this->sections) {
            return;
        }

        $totalRows = 0;
        foreach ($this->sections as $section) {
            if (!$section instanceof ListSection) {
                throw new InvalidArgumentException('All sections must be ListSection instances');
            }
            
            $totalRows += count($section->rows ?? []);
        }

        if ($totalRows > 10) {
            throw new InvalidArgumentException('List messages support maximum 10 options total');
        }
    }

    /**
     * Convert to WhatsApp API payload
     */
    public function toWhatsAppPayload(string $recipientPhone): array
    {
        if (!$this->type) {
            throw new InvalidArgumentException('Interactive type is required');
        }

        if (!$this->body) {
            throw new InvalidArgumentException('Body is required for interactive messages');
        }

        $data = [
            'header' => $this->header,
            'body' => $this->body,
            'footer' => $this->footer,
        ];

        switch ($this->type) {
            case InteractiveType::BUTTON:
                $data['buttons'] = $this->getButtonsPayload();
                break;
            case InteractiveType::LIST:
                $data['button_text'] = $this->button_text ?? 'Ver opções';
                $data['sections'] = $this->getSectionsPayload();
                break;
        }

        $payload = $this->type->toWhatsAppPayload($data);
        $payload['to'] = $recipientPhone;

        return $payload;
    }

    /**
     * Get buttons payload for WhatsApp API
     */
    private function getButtonsPayload(): array
    {
        if (!$this->buttons) {
            return [];
        }

        $buttonsPayload = [];
        foreach ($this->buttons as $button) {
            $buttonsPayload[] = $button->toWhatsAppPayload();
        }

        return $buttonsPayload;
    }

    /**
     * Get sections payload for WhatsApp API
     */
    private function getSectionsPayload(): array
    {
        if (!$this->sections) {
            return [];
        }

        $sectionsPayload = [];
        foreach ($this->sections as $section) {
            $sectionsPayload[] = $section->toWhatsAppPayload();
        }

        return $sectionsPayload;
    }

    /**
     * Add button to interactive message
     */
    public function addButton(Button $button): void
    {
        if ($this->type !== InteractiveType::BUTTON) {
            throw new InvalidArgumentException('Can only add buttons to BUTTON type interactive messages');
        }

        if (!$this->buttons) {
            $this->buttons = [];
        }

        if (count($this->buttons) >= 3) {
            throw new InvalidArgumentException('Maximum 3 buttons allowed');
        }

        $this->buttons[] = $button;
    }

    /**
     * Add section to interactive message
     */
    public function addSection(ListSection $section): void
    {
        if ($this->type !== InteractiveType::LIST) {
            throw new InvalidArgumentException('Can only add sections to LIST type interactive messages');
        }

        if (!$this->sections) {
            $this->sections = [];
        }

        // Check total rows limit
        $currentTotalRows = $this->getTotalRows();
        $newSectionRows = count($section->rows ?? []);
        
        if ($currentTotalRows + $newSectionRows > 10) {
            throw new InvalidArgumentException('Maximum 10 total rows allowed across all sections');
        }

        $this->sections[] = $section;
    }

    /**
     * Get total number of rows across all sections
     */
    public function getTotalRows(): int
    {
        if (!$this->sections) {
            return 0;
        }

        $total = 0;
        foreach ($this->sections as $section) {
            $total += count($section->rows ?? []);
        }

        return $total;
    }

    /**
     * Check if interactive message is valid
     */
    public function isValid(): bool
    {
        try {
            $this->validateLimits();
            return true;
        } catch (InvalidArgumentException $e) {
            return false;
        }
    }

    /**
     * Get validation errors
     */
    public function getValidationErrors(): array
    {
        $errors = [];

        if (!$this->body) {
            $errors[] = 'Body is required';
        }

        if (!$this->type) {
            $errors[] = 'Interactive type is required';
        }

        try {
            $this->validateLimits();
        } catch (InvalidArgumentException $e) {
            $errors[] = $e->getMessage();
        }

        return $errors;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'header' => $this->header,
            'body' => $this->body,
            'footer' => $this->footer,
            'type' => $this->type?->value,
            'button_text' => $this->button_text,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'buttons' => $this->buttons ? array_map(fn($button) => $button->toArray(), $this->buttons) : null,
            'sections' => $this->sections ? array_map(fn($section) => $section->toArray(), $this->sections) : null,
            'total_rows' => $this->getTotalRows(),
            'is_valid' => $this->isValid(),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            'organization_id' => $this->organization_id,
            'header' => $this->header,
            'body' => $this->body,
            'footer' => $this->footer,
            'type' => $this->type?->value,
            'button_text' => $this->button_text,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'header' => $this->header,
            'body' => $this->body,
            'footer' => $this->footer,
            'type' => $this->type?->value,
            'button_text' => $this->button_text,
        ];
    }
}
