<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class InteractiveMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'header',
        'body',
        'footer',
        'type',
        'button_text',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the organization that owns the interactive message
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the buttons associated with this interactive message
     */
    public function buttons(): BelongsToMany
    {
        return $this->belongsToMany(Button::class, 'interactive_message_button')
                    ->withTimestamps();
    }

    /**
     * Get the sections for this interactive message
     */
    public function sections(): HasMany
    {
        return $this->hasMany(ListSection::class);
    }
}
