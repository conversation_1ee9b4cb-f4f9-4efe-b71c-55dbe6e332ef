<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ListSection extends Model
{
    use HasFactory;

    protected $fillable = [
        'interactive_message_id',
        'title',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the interactive message that owns this section
     */
    public function interactiveMessage(): BelongsTo
    {
        return $this->belongsTo(InteractiveMessage::class);
    }

    /**
     * Get the rows for this section
     */
    public function rows(): HasMany
    {
        return $this->hasMany(ListRow::class);
    }
}
