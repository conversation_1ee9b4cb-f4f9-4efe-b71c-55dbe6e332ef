<?php

namespace App\Http\Controllers\ChatBot;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\TemplateFilters;
use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Http\Requests\Template\StoreRequest;
use App\Http\Requests\Template\UpdateRequest;
use App\Http\Requests\Template\SaveFullTemplateRequest;
use App\UseCases\ChatBot\Template\Delete;
use App\UseCases\ChatBot\Template\Get;
use App\UseCases\ChatBot\Template\GetAll;
use App\UseCases\ChatBot\Template\RePublishTemplate;
use App\UseCases\ChatBot\Template\SaveFullTemplate;
use App\UseCases\ChatBot\Template\Store;
use App\UseCases\ChatBot\Template\Update;
use App\UseCases\ChatBot\Template\PublishTemplate;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Enums\PublishingService;

class TemplateController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $getAll = $useCase->perform(
                new TemplateFilters(request()->all()),
                new OrderBy(request()->only(OrderBy::ALLOWED_FIELDS))
            );

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store(StoreRequest $request) : JsonResponse {
        try{
            /** @var Store $useCase */
            $useCase = app()->make(Store::class);
            $template = $useCase->perform($request);

            return $this->response(
                "Template created successfully",
                "success",
                200 ,
                $template->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update(UpdateRequest $request, int $id) : JsonResponse {
        try{
            /** @var Update $useCase */
            $useCase = app()->make(Update::class);
            $update = $useCase->perform($request, $id);

            return $this->response(
                "Template updated successfully",
                "success",
                200 ,
                $update->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function destroy(int $id) : JsonResponse {
        try{
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $delete = $useCase->perform($id);

            return $this->response(
                "Template deleted successfully",
                "success",
                200 ,
                [$delete]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Publishes a template using the PublishTemplate UseCase.
     *
     * @param int $id Template ID to be published
     * @return JsonResponse
     */
    public function publishToWhatsapp(int $id): JsonResponse
    {
        try {
            $service = PublishingService::whatsapp;

            /** @var Get $getUseCase */
            $getUseCase = app()->make(Get::class);
            $templateDomain = $getUseCase->perform($id);

            /** @var PublishTemplate $publishUseCase */
            $publishUseCase = app()->make(PublishTemplate::class);
            $publishing = $publishUseCase->perform($templateDomain, $service);

            return $this->response(
                "Template published successfully",
                "success",
                200,
                $publishing->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

     /**
     * Publishes a template using the PublishTemplate UseCase.
     *
     * @param int $id Template ID to be published
     * @return JsonResponse
     */
    public function rePublishToWhatsapp(int $id): JsonResponse
    {
        try {
            /** @var Get $getUseCase */
            $getUseCase = app()->make(Get::class);
            $templateDomain = $getUseCase->perform($id);

            /** @var RePublishTemplate $publishUseCase */
            $publishUseCase = app()->make(RePublishTemplate::class);
            $publishing = $publishUseCase->perform($templateDomain);

            return $this->response(
                "Template re-published successfully",
                "success",
                200,
                $publishing->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function saveFullTemplate(SaveFullTemplateRequest $request) : JsonResponse {
        try{
            /** @var SaveFullTemplate $useCase */
            $useCase = app()->make(SaveFullTemplate::class);
            $template = $useCase->perform($request);

            return $this->response(
                "Template created successfully",
                "success",
                200 ,
                $template->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getToWhatsAppPayload(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $template = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $template->toWhatsAppPayload()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

}
