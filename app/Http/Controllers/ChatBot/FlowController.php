<?php

namespace App\Http\Controllers\ChatBot;

use App\Domains\Filters\FlowFilters;
use App\Domains\Filters\OrderBy;
use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Http\Requests\Flow\SaveFullFlowRequest;
use App\Http\Requests\Flow\StoreRequest;
use App\Http\Requests\Flow\UpdateRequest;
use App\UseCases\ChatBot\Flow\Delete;
use App\UseCases\ChatBot\Flow\Get;
use App\UseCases\ChatBot\Flow\GetAll;
use App\UseCases\ChatBot\Flow\SaveFullFlow;
use App\UseCases\ChatBot\Flow\Store;
use App\UseCases\ChatBot\Flow\Update;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class FlowController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $getAll = $useCase->perform(
                new FlowFilters(request()->all()),
                new OrderBy(request()->only(OrderBy::ALLOWED_FIELDS))
            );

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store(StoreRequest $request) : JsonResponse {
        try{
            /** @var Store $useCase */
            $useCase = app()->make(Store::class);
            $flow = $useCase->perform($request);

            return $this->response(
                "Flow created successfully",
                "success",
                200 ,
                $flow->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update(UpdateRequest $request, int $id) : JsonResponse {
        try{
            /** @var Update $useCase */
            $useCase = app()->make(Update::class);
            $update = $useCase->perform($request, $id);

            return $this->response(
                "Flow updated successfully",
                "success",
                200 ,
                $update->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function destroy(int $id) : JsonResponse {
        try{
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $delete = $useCase->perform($id);

            return $this->response(
                "Flow deleted successfully",
                "success",
                200 ,
                [$delete]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Save a complete flow with all its steps, components, buttons and parameters
     *
     * @param SaveFullFlowRequest $request Validated request with flow structure
     * @return JsonResponse
     */
    public function saveFullFlow(SaveFullFlowRequest $request) : JsonResponse {
        $startTime = microtime(true);
        $organizationId = auth()->user()->organization_id ?? null;
        $userId = auth()->user()->id ?? null;

        // Log operation start
        Log::info('SaveFullFlow operation started', [
            'user_id' => $userId,
            'organization_id' => $organizationId,
            'flow_name' => $request->input('flow.name'),
            'steps_count' => count($request->input('steps', [])),
            'operation_id' => uniqid('saveflow_')
        ]);

        try {
            // Check authorization
            if (!$organizationId) {
                Log::warning('SaveFullFlow unauthorized access attempt', [
                    'user_id' => $userId,
                    'ip' => $request->ip()
                ]);

                return $this->response(
                    "Unauthorized: Organization required",
                    "error",
                    403,
                    []
                );
            }

            /** @var SaveFullFlow $useCase */
            $useCase = app()->make(SaveFullFlow::class);
            $flow = $useCase->perform($request);

            $executionTime = round((microtime(true) - $startTime) * 1000, 2);

            // Log successful operation
            Log::info('SaveFullFlow operation completed successfully', [
                'user_id' => $userId,
                'organization_id' => $organizationId,
                'flow_id' => $flow->id,
                'flow_name' => $flow->name,
                'steps_count' => $flow->steps_count,
                'execution_time_ms' => $executionTime
            ]);

            // Check for validation warnings
            $warnings = session('flow_validation_warnings', []);
            if (!empty($warnings)) {
                session()->forget('flow_validation_warnings');
            }

            return $this->response(
                $flow->id ? "Flow updated successfully" : "Flow created successfully",
                "success",
                200,
                $flow->toArray(),
                null,
                [
                    'execution_time_ms' => $executionTime,
                    'warnings' => $warnings
                ]
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('SaveFullFlow validation failed', [
                'user_id' => $userId,
                'organization_id' => $organizationId,
                'errors' => $e->errors(),
                'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ]);

            return $this->response(
                "Validation failed",
                "error",
                422,
                [],
                $e->errors()
            );

        } catch (\Illuminate\Database\QueryException $e) {
            Log::error('SaveFullFlow database error', [
                'user_id' => $userId,
                'organization_id' => $organizationId,
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ]);

            return $this->response(
                "Database error occurred while saving flow",
                "error",
                500,
                []
            );

        } catch (\Throwable $e) {
            Log::error('SaveFullFlow unexpected error', [
                'user_id' => $userId,
                'organization_id' => $organizationId,
                'error_class' => get_class($e),
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString(),
                'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ]);

            return $this->response(
                "An unexpected error occurred while saving flow",
                "error",
                500,
                []
            );
        }
    }
}
