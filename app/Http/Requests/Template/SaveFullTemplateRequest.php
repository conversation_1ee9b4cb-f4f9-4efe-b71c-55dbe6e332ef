<?php

namespace App\Http\Requests\Template;

use App\Helpers\Traits\Response;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class SaveFullTemplateRequest extends FormRequest
{
    use Response;

    private const ERROR_STATUS = "error";
    private const ERROR_CODE = 422;
    private const ERROR_MESSAGE = "Validation Failed";

    // WhatsApp Business API constants
    private const WHATSAPP_CATEGORIES = ['MARKETING', 'UTILITY', 'AUTHENTICATION'];
    private const WHATSAPP_LANGUAGES = [
        'en_US', 'en_GB', 'es_ES', 'es_MX', 'pt_BR', 'pt_PT', 'fr_FR', 'de_DE',
        'it_IT', 'ru_RU', 'ar', 'hi', 'zh_CN', 'zh_TW', 'ja', 'ko'
    ];
    private const COMPONENT_TYPES = ['HEADER', 'BODY', 'FOOTER', 'BUTTONS'];
    private const HEADER_FORMATS = ['TEXT', 'IMAGE', 'VIDEO', 'DOCUMENT', 'LOCATION'];
    private const BUTTON_TYPES = ['QUICK_REPLY', 'URL', 'PHONE_NUMBER', 'COPY_CODE', 'FLOW'];

    // WhatsApp limits
    private const MAX_TEMPLATE_NAME_LENGTH = 512;
    private const MAX_HEADER_TEXT_LENGTH = 60;
    private const MAX_BODY_TEXT_LENGTH = 1024;
    private const MAX_FOOTER_TEXT_LENGTH = 60;
    private const MAX_BUTTON_TEXT_LENGTH = 25;
    private const MAX_URL_LENGTH = 2000;
    private const MAX_BUTTONS_PER_TEMPLATE = 10;
    private const MAX_QUICK_REPLY_BUTTONS = 10;
    private const MAX_URL_BUTTONS = 2;
    private const MAX_CALL_BUTTONS = 1;

    public function authorize()
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                self::ERROR_MESSAGE,
                self::ERROR_STATUS,
                self::ERROR_CODE,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules()
    {
        return [
            'template_json' => 'required',
            'phone_number_id' => 'nullable|integer|exists:phone_numbers,id',
        ];
    }

    public function messages()
    {
        return [
            'template_json.required' => 'The template JSON is required.',
            'template_json.json' => 'The template JSON must be valid JSON.',
            'template_json.name.required' => 'Template name is required.',
            'template_json.name.regex' => 'Template name can only contain lowercase letters, numbers, and underscores.',
            'template_json.name.max' => 'Template name cannot exceed ' . self::MAX_TEMPLATE_NAME_LENGTH . ' characters.',
            'template_json.category.required' => 'Template category is required.',
            'template_json.category.in' => 'Template category must be one of: ' . implode(', ', self::WHATSAPP_CATEGORIES),
            'template_json.language.required' => 'Template language is required.',
            'template_json.language.in' => 'Template language must be one of the supported WhatsApp languages.',
            'template_json.components.required' => 'Template must have at least one component.',
            'template_json.components.min' => 'Template must have at least one component.',
            'template_json.components.*.type.required' => 'Component type is required.',
            'template_json.components.*.type.in' => 'Component type must be one of: ' . implode(', ', self::COMPONENT_TYPES),
            'template_json.components.*.format.required_if' => 'Header format is required for header components.',
            'template_json.components.*.format.in' => 'Header format must be one of: ' . implode(', ', self::HEADER_FORMATS),
            'template_json.components.*.text.required_if' => 'Text is required for header, body, and footer components.',
            'template_json.components.*.buttons.max' => 'Maximum ' . self::MAX_BUTTONS_PER_TEMPLATE . ' buttons allowed per template.',
            'template_json.components.*.buttons.*.type.required' => 'Button type is required.',
            'template_json.components.*.buttons.*.type.in' => 'Button type must be one of: ' . implode(', ', self::BUTTON_TYPES),
            'template_json.components.*.buttons.*.text.required' => 'Button text is required.',
            'template_json.components.*.buttons.*.text.max' => 'Button text cannot exceed ' . self::MAX_BUTTON_TEXT_LENGTH . ' characters.',
            'template_json.components.*.buttons.*.url.required_if' => 'URL is required for URL buttons.',
            'template_json.components.*.buttons.*.url.url' => 'Button URL must be a valid URL.',
            'template_json.components.*.buttons.*.url.max' => 'Button URL cannot exceed ' . self::MAX_URL_LENGTH . ' characters.',
            'template_json.components.*.buttons.*.phone_number.required_if' => 'Phone number is required for phone number buttons.',
            'template_json.components.*.buttons.*.phone_number.regex' => 'Phone number must be in international format (+1234567890).',
            'template_json.components.*.parameters.*.type.required' => 'Parameter type is required.',
            'template_json.components.*.parameters.*.type.in' => 'Parameter type must be one of: text, currency, date_time, image, document, video.',
            'template_json.components.*.parameters.*.text.required_if' => 'Parameter text is required for text parameters.',
            'phone_number_id.integer' => 'Phone number ID must be an integer.',
            'phone_number_id.exists' => 'The specified phone number does not exist.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $this->validateTemplateJson($validator);
        });
    }

    /**
     * Validate the entire template JSON structure
     */
    private function validateTemplateJson($validator)
    {
        $templateJson = $this->input('template_json');

        // Parse JSON if it's a string
        if (is_string($templateJson)) {
            $templateJson = json_decode($templateJson, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $validator->errors()->add('template_json', 'Invalid JSON format.');
                return;
            }
        }

        if (!is_array($templateJson)) {
            $validator->errors()->add('template_json', 'Template JSON must be an object.');
            return;
        }

        // Validate basic template fields
        $this->validateBasicFields($validator, $templateJson);

        // Validate components
        $this->validateComponents($validator, $templateJson);

        // Validate button limits
        $this->validateButtonLimits($validator, $templateJson);

        // Validate component structure
        $this->validateComponentStructure($validator, $templateJson);

        // Validate placeholders
        $this->validatePlaceholders($validator, $templateJson);
    }

    /**
     * Validate basic template fields
     */
    private function validateBasicFields($validator, $templateJson)
    {
        // Name validation
        if (empty($templateJson['name'])) {
            $validator->errors()->add('template_json.name', 'Template name is required.');
        } elseif (!is_string($templateJson['name'])) {
            $validator->errors()->add('template_json.name', 'Template name must be a string.');
        } elseif (strlen($templateJson['name']) > self::MAX_TEMPLATE_NAME_LENGTH) {
            $validator->errors()->add('template_json.name', 'Template name cannot exceed ' . self::MAX_TEMPLATE_NAME_LENGTH . ' characters.');
        } elseif (!preg_match('/^[a-z0-9_]+$/', $templateJson['name'])) {
            $validator->errors()->add('template_json.name', 'Template name can only contain lowercase letters, numbers, and underscores.');
        }

        // Category validation
        if (empty($templateJson['category'])) {
            $validator->errors()->add('template_json.category', 'Template category is required.');
        } elseif (!in_array($templateJson['category'], self::WHATSAPP_CATEGORIES)) {
            $validator->errors()->add('template_json.category', 'Template category must be one of: ' . implode(', ', self::WHATSAPP_CATEGORIES));
        }

        // Language validation
        if (empty($templateJson['language'])) {
            $validator->errors()->add('template_json.language', 'Template language is required.');
        } elseif (!in_array($templateJson['language'], self::WHATSAPP_LANGUAGES)) {
            $validator->errors()->add('template_json.language', 'Template language must be one of the supported WhatsApp languages.');
        }
    }

    /**
     * Validate components structure and content
     */
    private function validateComponents($validator, $templateJson)
    {
        if (empty($templateJson['components']) || !is_array($templateJson['components'])) {
            $validator->errors()->add('template_json.components', 'Template must have at least one component.');
            return;
        }

        foreach ($templateJson['components'] as $index => $component) {
            $this->validateComponent($validator, $component, $index);
        }
    }

    /**
     * Validate individual component
     */
    private function validateComponent($validator, $component, $index)
    {
        // Type validation
        if (empty($component['type'])) {
            $validator->errors()->add("template_json.components.{$index}.type", 'Component type is required.');
            return;
        }

        if (!in_array($component['type'], self::COMPONENT_TYPES)) {
            $validator->errors()->add("template_json.components.{$index}.type", 'Component type must be one of: ' . implode(', ', self::COMPONENT_TYPES));
            return;
        }

        // Text validation based on component type
        if (in_array($component['type'], ['HEADER', 'BODY', 'FOOTER'])) {
            $this->validateComponentText($validator, $component, $index);
        }

        // Header format validation
        if ($component['type'] === 'HEADER') {
            if (empty($component['format'])) {
                $validator->errors()->add("template_json.components.{$index}.format", 'Header format is required.');
            } elseif (!in_array($component['format'], self::HEADER_FORMATS)) {
                $validator->errors()->add("template_json.components.{$index}.format", 'Header format must be one of: ' . implode(', ', self::HEADER_FORMATS));
            }
        }

        // Buttons validation
        if ($component['type'] === 'BUTTONS' && isset($component['buttons'])) {
            $this->validateComponentButtons($validator, $component['buttons'], $index);
        }
    }

    /**
     * Validate component text based on type
     */
    private function validateComponentText($validator, $component, $index)
    {
        if (empty($component['text'])) {
            $validator->errors()->add("template_json.components.{$index}.text", 'Text is required for ' . strtolower($component['type']) . ' components.');
            return;
        }

        $text = $component['text'];
        $type = $component['type'];

        switch ($type) {
            case 'HEADER':
                if (strlen($text) > self::MAX_HEADER_TEXT_LENGTH) {
                    $validator->errors()->add("template_json.components.{$index}.text", "Header text cannot exceed " . self::MAX_HEADER_TEXT_LENGTH . " characters.");
                }
                break;
            case 'BODY':
                if (strlen($text) > self::MAX_BODY_TEXT_LENGTH) {
                    $validator->errors()->add("template_json.components.{$index}.text", "Body text cannot exceed " . self::MAX_BODY_TEXT_LENGTH . " characters.");
                }
                if (preg_match('/[\n\t]/', $text) || preg_match('/    /', $text)) {
                    $validator->errors()->add("template_json.components.{$index}.text", "Body text cannot contain newlines, tabs, or more than 3 consecutive spaces.");
                }
                break;
            case 'FOOTER':
                if (strlen($text) > self::MAX_FOOTER_TEXT_LENGTH) {
                    $validator->errors()->add("template_json.components.{$index}.text", "Footer text cannot exceed " . self::MAX_FOOTER_TEXT_LENGTH . " characters.");
                }
                break;
        }
    }

    /**
     * Validate component buttons
     */
    private function validateComponentButtons($validator, $buttons, $componentIndex)
    {
        if (!is_array($buttons)) {
            $validator->errors()->add("template_json.components.{$componentIndex}.buttons", 'Buttons must be an array.');
            return;
        }

        if (count($buttons) > self::MAX_BUTTONS_PER_TEMPLATE) {
            $validator->errors()->add("template_json.components.{$componentIndex}.buttons", 'Maximum ' . self::MAX_BUTTONS_PER_TEMPLATE . ' buttons allowed per template.');
        }

        foreach ($buttons as $buttonIndex => $button) {
            $this->validateButton($validator, $button, $componentIndex, $buttonIndex);
        }
    }

    /**
     * Validate individual button
     */
    private function validateButton($validator, $button, $componentIndex, $buttonIndex)
    {
        $path = "template_json.components.{$componentIndex}.buttons.{$buttonIndex}";

        // Type validation
        if (empty($button['type'])) {
            $validator->errors()->add("{$path}.type", 'Button type is required.');
            return;
        }

        if (!in_array($button['type'], self::BUTTON_TYPES)) {
            $validator->errors()->add("{$path}.type", 'Button type must be one of: ' . implode(', ', self::BUTTON_TYPES));
        }

        // Text validation
        if (empty($button['text'])) {
            $validator->errors()->add("{$path}.text", 'Button text is required.');
        } elseif (strlen($button['text']) > self::MAX_BUTTON_TEXT_LENGTH) {
            $validator->errors()->add("{$path}.text", 'Button text cannot exceed ' . self::MAX_BUTTON_TEXT_LENGTH . ' characters.');
        }

        // Type-specific validations
        switch ($button['type']) {
            case 'URL':
                if (empty($button['url'])) {
                    $validator->errors()->add("{$path}.url", 'URL is required for URL buttons.');
                } elseif (strlen($button['url']) > self::MAX_URL_LENGTH) {
                    $validator->errors()->add("{$path}.url", 'Button URL cannot exceed ' . self::MAX_URL_LENGTH . ' characters.');
                } elseif (!filter_var($button['url'], FILTER_VALIDATE_URL)) {
                    $validator->errors()->add("{$path}.url", 'Button URL must be a valid URL.');
                }
                break;
            case 'PHONE_NUMBER':
                if (empty($button['phone_number'])) {
                    $validator->errors()->add("{$path}.phone_number", 'Phone number is required for phone number buttons.');
                } elseif (!preg_match('/^\+[1-9]\d{1,14}$/', $button['phone_number'])) {
                    $validator->errors()->add("{$path}.phone_number", 'Phone number must be in international format (+1234567890).');
                }
                break;
        }
    }

    /**
     * Validate button type limits per template
     */
    private function validateButtonLimits($validator, $templateJson)
    {
        if (!isset($templateJson['components'])) {
            return;
        }

        $quickReplyCount = 0;
        $urlCount = 0;
        $callCount = 0;

        foreach ($templateJson['components'] as $component) {
            if (isset($component['buttons']) && is_array($component['buttons'])) {
                foreach ($component['buttons'] as $button) {
                    switch ($button['type'] ?? '') {
                        case 'QUICK_REPLY':
                            $quickReplyCount++;
                            break;
                        case 'URL':
                            $urlCount++;
                            break;
                        case 'PHONE_NUMBER':
                            $callCount++;
                            break;
                    }
                }
            }
        }

        if ($quickReplyCount > self::MAX_QUICK_REPLY_BUTTONS) {
            $validator->errors()->add('template_json.components.buttons',
                'Maximum ' . self::MAX_QUICK_REPLY_BUTTONS . ' quick reply buttons allowed.');
        }

        if ($urlCount > self::MAX_URL_BUTTONS) {
            $validator->errors()->add('template_json.components.buttons',
                'Maximum ' . self::MAX_URL_BUTTONS . ' URL buttons allowed.');
        }

        if ($callCount > self::MAX_CALL_BUTTONS) {
            $validator->errors()->add('template_json.components.buttons',
                'Maximum ' . self::MAX_CALL_BUTTONS . ' call button allowed.');
        }
    }

    /**
     * Validate component structure requirements
     */
    private function validateComponentStructure($validator)
    {
        $templateJson = $this->input('template_json');
        if (!is_array($templateJson) || !isset($templateJson['components'])) {
            return;
        }

        $hasBody = false;
        $headerCount = 0;
        $footerCount = 0;
        $buttonCount = 0;

        foreach ($templateJson['components'] as $component) {
            switch ($component['type'] ?? '') {
                case 'HEADER':
                    $headerCount++;
                    break;
                case 'BODY':
                    $hasBody = true;
                    break;
                case 'FOOTER':
                    $footerCount++;
                    break;
                case 'BUTTONS':
                    $buttonCount++;
                    break;
            }
        }

        if (!$hasBody) {
            $validator->errors()->add('template_json.components',
                'Template must have exactly one BODY component.');
        }

        if ($headerCount > 1) {
            $validator->errors()->add('template_json.components',
                'Template can have at most one HEADER component.');
        }

        if ($footerCount > 1) {
            $validator->errors()->add('template_json.components',
                'Template can have at most one FOOTER component.');
        }

        if ($buttonCount > 1) {
            $validator->errors()->add('template_json.components',
                'Template can have at most one BUTTONS component.');
        }
    }

    /**
     * Validate placeholder consistency
     */
    private function validatePlaceholders($validator)
    {
        $templateJson = $this->input('template_json');
        if (!is_array($templateJson) || !isset($templateJson['components'])) {
            return;
        }

        foreach ($templateJson['components'] as $componentIndex => $component) {
            if (isset($component['text'])) {
                $text = $component['text'];

                // Find all placeholders in format {{1}}, {{2}}, etc.
                preg_match_all('/\{\{(\d+)\}\}/', $text, $matches);
                $placeholders = array_map('intval', $matches[1]);

                if (!empty($placeholders)) {
                    // Check if placeholders are sequential starting from 1
                    sort($placeholders);
                    $expected = range(1, count($placeholders));

                    if ($placeholders !== $expected) {
                        $validator->errors()->add("template_json.components.{$componentIndex}.text",
                            'Placeholders must be sequential starting from {{1}}.');
                    }
                }
            }
        }
    }
}
