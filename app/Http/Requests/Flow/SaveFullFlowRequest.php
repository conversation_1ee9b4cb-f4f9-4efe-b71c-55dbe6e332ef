<?php

namespace App\Http\Requests\Flow;

use App\Helpers\Traits\Response;
use App\Enums\ChatBot\FlowStatus;
use App\Enums\ChatBot\StepType;
use App\Enums\ChatBot\ComponentFormat;
use App\Enums\ChatBot\WhatsAppButtonType;
use App\Services\ChatBot\FlowValidationService;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class SaveFullFlowRequest extends FormRequest
{
    use Response;

    private const ERROR_STATUS = "error";
    private const ERROR_CODE = 422;
    private const ERROR_MESSAGE = "Validation Failed";

    public function authorize()
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                self::ERROR_MESSAGE,
                self::ERROR_STATUS,
                self::ERROR_CODE,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules()
    {
        return [
            // Flow validation
            'flow' => 'required|array',
            'flow.id' => 'nullable|integer|exists:flows,id',
            'flow.name' => 'required|string|max:255',
            'flow.description' => 'nullable|string|max:1000',
            'flow.is_default_flow' => 'nullable|boolean',
            'flow.inactivity_minutes' => 'nullable|integer|min:1|max:1440',
            'flow.ending_conversation_message' => 'nullable|string|max:500',
            'flow.version' => 'nullable|string|max:10',
            'flow.status' => ['nullable', Rule::in(FlowStatus::getAll())],
            'flow.variables' => 'nullable|array',

            // Steps validation
            'steps' => 'required|array|min:1',
            'steps.*.id' => 'nullable|integer|exists:steps,id',
            'steps.*.step' => 'required|string|max:255',
            'steps.*.type' => 'nullable|string|max:50',
            'steps.*.step_type' => ['nullable', Rule::in(StepType::getAll())],
            'steps.*.position' => 'required|integer|min:0',
            'steps.*.next_step' => 'nullable|integer',
            'steps.*.earlier_step' => 'nullable|integer',
            'steps.*.is_initial_step' => 'nullable|boolean',
            'steps.*.is_ending_step' => 'nullable|boolean',
            'steps.*.configuration' => 'nullable|array',
            'steps.*.navigation_rules' => 'nullable|array',
            'steps.*.timeout_seconds' => 'nullable|integer|min:1|max:3600',
            'steps.*.input' => 'nullable|string|max:255',

            // Component validation
            'steps.*.component' => 'nullable|array',
            'steps.*.component.id' => 'nullable|integer|exists:components,id',
            'steps.*.component.name' => 'nullable|string|max:255',
            'steps.*.component.type' => 'nullable|string|max:50',
            'steps.*.component.sub_type' => 'nullable|string|max:50',
            'steps.*.component.index' => 'nullable|integer|min:0',
            'steps.*.component.text' => 'nullable|string|max:4096',
            'steps.*.component.format' => ['nullable', Rule::in(ComponentFormat::getAll())],

            // Buttons validation
            'steps.*.component.buttons' => 'nullable|array',
            'steps.*.component.buttons.*.id' => 'nullable|integer|exists:buttons,id',
            'steps.*.component.buttons.*.text' => 'required_with:steps.*.component.buttons|string|max:20',
            'steps.*.component.buttons.*.type' => ['nullable', Rule::in(WhatsAppButtonType::getAll())],
            'steps.*.component.buttons.*.internal_type' => 'nullable|string|max:50',
            'steps.*.component.buttons.*.internal_data' => 'nullable|string|max:500',
            'steps.*.component.buttons.*.callback_data' => 'nullable|string|max:1000',

            // Alternative buttons structure (action.buttons)
            'steps.*.component.action' => 'nullable|array',
            'steps.*.component.action.buttons' => 'nullable|array',
            'steps.*.component.action.buttons.*.id' => 'nullable|integer|exists:buttons,id',
            'steps.*.component.action.buttons.*.text' => 'required_with:steps.*.component.action.buttons|string|max:20',
            'steps.*.component.action.buttons.*.type' => ['nullable', Rule::in(WhatsAppButtonType::getAll())],
            'steps.*.component.action.buttons.*.internal_type' => 'nullable|string|max:50',
            'steps.*.component.action.buttons.*.internal_data' => 'nullable|string|max:500',
            'steps.*.component.action.buttons.*.callback_data' => 'nullable|string|max:1000',

            // Parameters validation
            'steps.*.component.parameters' => 'nullable|array',
            'steps.*.component.parameters.*.id' => 'nullable|integer|exists:parameters,id',
            'steps.*.component.parameters.*.type' => 'nullable|string|max:50',
            'steps.*.component.parameters.*.text' => 'nullable|string|max:255',
            'steps.*.component.parameters.*.index' => 'nullable|integer|min:0',
        ];
    }

    public function messages()
    {
        return [
            // Flow messages
            'flow.required' => 'O campo flow é obrigatório.',
            'flow.array' => 'O campo flow deve ser um array.',
            'flow.id.exists' => 'O flow especificado não existe.',
            'flow.name.required' => 'O nome do flow é obrigatório.',
            'flow.name.max' => 'O nome do flow não pode ter mais de 255 caracteres.',
            'flow.description.max' => 'A descrição do flow não pode ter mais de 1000 caracteres.',
            'flow.inactivity_minutes.min' => 'Os minutos de inatividade devem ser pelo menos 1.',
            'flow.inactivity_minutes.max' => 'Os minutos de inatividade não podem exceder 1440 (24 horas).',
            'flow.ending_conversation_message.max' => 'A mensagem de fim de conversa não pode ter mais de 500 caracteres.',
            'flow.version.max' => 'A versão não pode ter mais de 10 caracteres.',
            'flow.status.in' => 'O status deve ser um dos valores válidos: ' . implode(', ', FlowStatus::getAll()),

            // Steps messages
            'steps.required' => 'O campo steps é obrigatório.',
            'steps.array' => 'O campo steps deve ser um array.',
            'steps.min' => 'Deve haver pelo menos 1 step.',
            'steps.*.id.exists' => 'O step especificado não existe.',
            'steps.*.step.required' => 'O identificador do step é obrigatório.',
            'steps.*.step.max' => 'O identificador do step não pode ter mais de 255 caracteres.',
            'steps.*.step_type.in' => 'O tipo do step deve ser um dos valores válidos: ' . implode(', ', StepType::getAll()),
            'steps.*.position.required' => 'A posição do step é obrigatória.',
            'steps.*.position.min' => 'A posição do step deve ser pelo menos 0.',
            'steps.*.timeout_seconds.min' => 'O timeout deve ser pelo menos 1 segundo.',
            'steps.*.timeout_seconds.max' => 'O timeout não pode exceder 3600 segundos (1 hora).',

            // Component messages
            'steps.*.component.id.exists' => 'O componente especificado não existe.',
            'steps.*.component.name.max' => 'O nome do componente não pode ter mais de 255 caracteres.',
            'steps.*.component.text.max' => 'O texto do componente não pode ter mais de 4096 caracteres.',
            'steps.*.component.format.in' => 'O formato deve ser um dos valores válidos: ' . implode(', ', ComponentFormat::getAll()),

            // Buttons messages
            'steps.*.component.buttons.*.id.exists' => 'O botão especificado não existe.',
            'steps.*.component.buttons.*.text.required_with' => 'O texto do botão é obrigatório.',
            'steps.*.component.buttons.*.text.max' => 'O texto do botão não pode ter mais de 20 caracteres.',
            'steps.*.component.buttons.*.type.in' => 'O tipo do botão deve ser um dos valores válidos: ' . implode(', ', WhatsAppButtonType::getAll()),
            'steps.*.component.buttons.*.internal_data.max' => 'Os dados internos do botão não podem ter mais de 500 caracteres.',
            'steps.*.component.buttons.*.callback_data.max' => 'Os dados de callback do botão não podem ter mais de 1000 caracteres.',

            // Action buttons messages
            'steps.*.component.action.buttons.*.id.exists' => 'O botão de ação especificado não existe.',
            'steps.*.component.action.buttons.*.text.required_with' => 'O texto do botão de ação é obrigatório.',
            'steps.*.component.action.buttons.*.text.max' => 'O texto do botão de ação não pode ter mais de 20 caracteres.',
            'steps.*.component.action.buttons.*.type.in' => 'O tipo do botão de ação deve ser um dos valores válidos: ' . implode(', ', WhatsAppButtonType::getAll()),

            // Parameters messages
            'steps.*.component.parameters.*.id.exists' => 'O parâmetro especificado não existe.',
            'steps.*.component.parameters.*.text.max' => 'O texto do parâmetro não pode ter mais de 255 caracteres.',
            'steps.*.component.parameters.*.index.min' => 'O índice do parâmetro deve ser pelo menos 0.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Ensure steps have sequential positions if not provided
        if ($this->has('steps') && is_array($this->steps)) {
            $steps = $this->steps;
            foreach ($steps as $index => &$step) {
                if (!isset($step['position'])) {
                    $step['position'] = $index;
                }
            }
            $this->merge(['steps' => $steps]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $this->validateFlowIntegrity($validator);
            $this->validateStepNavigation($validator);
            $this->validateButtonLimits($validator);
            $this->validateAdvancedRelationships($validator);
        });
    }

    /**
     * Validate advanced relationships using FlowValidationService
     */
    private function validateAdvancedRelationships($validator)
    {
        /** @var FlowValidationService $flowValidationService */
        $flowValidationService = app()->make(FlowValidationService::class);

        $flowData = [
            'flow' => $this->input('flow', []),
            'steps' => $this->input('steps', [])
        ];

        $validation = $flowValidationService->validateFlowStructure($flowData);

        // Add errors to validator
        foreach ($validation['errors'] as $error) {
            $validator->errors()->add('flow_structure', $error);
        }

        // Add warnings as info (they don't fail validation but are logged)
        if (!empty($validation['warnings'])) {
            $this->addWarningsToSession($validation['warnings']);
        }
    }

    /**
     * Add warnings to session for display to user
     */
    private function addWarningsToSession(array $warnings)
    {
        if (session()->has('flow_validation_warnings')) {
            $existingWarnings = session('flow_validation_warnings', []);
            $warnings = array_merge($existingWarnings, $warnings);
        }

        session(['flow_validation_warnings' => $warnings]);
    }

    /**
     * Validate flow integrity rules
     */
    private function validateFlowIntegrity($validator)
    {
        $steps = $this->input('steps', []);

        if (empty($steps)) {
            return;
        }

        // Check for initial step
        $hasInitialStep = false;
        $hasEndingStep = false;
        $positions = [];

        foreach ($steps as $index => $step) {
            $position = $step['position'] ?? $index;

            // Check for duplicate positions
            if (in_array($position, $positions)) {
                $validator->errors()->add("steps.{$index}.position", "Posição duplicada encontrada: {$position}");
            }
            $positions[] = $position;

            // Check for initial step
            if (($step['is_initial_step'] ?? false) || $position === 0) {
                $hasInitialStep = true;
            }

            // Check for ending step
            if ($step['is_ending_step'] ?? false) {
                $hasEndingStep = true;
            }
        }

        if (!$hasInitialStep) {
            $validator->errors()->add('steps', 'O flow deve ter pelo menos um step inicial.');
        }

        if (!$hasEndingStep) {
            // Add as warning instead of error
            $this->addWarningsToSession(['O flow deve ter pelo menos um step final.']);
        }
    }

    /**
     * Validate step navigation logic
     */
    private function validateStepNavigation($validator)
    {
        $steps = $this->input('steps', []);
        $stepPositions = array_column($steps, 'position');

        foreach ($steps as $index => $step) {
            $nextStep = $step['next_step'] ?? null;
            $earlierStep = $step['earlier_step'] ?? null;

            // Validate next_step references
            if ($nextStep !== null && !in_array($nextStep, $stepPositions)) {
                $validator->errors()->add("steps.{$index}.next_step", "Referência inválida para next_step: {$nextStep}");
            }

            // Validate earlier_step references
            if ($earlierStep !== null && !in_array($earlierStep, $stepPositions)) {
                $validator->errors()->add("steps.{$index}.earlier_step", "Referência inválida para earlier_step: {$earlierStep}");
            }
        }
    }

    /**
     * Validate WhatsApp button limits
     */
    private function validateButtonLimits($validator)
    {
        $steps = $this->input('steps', []);

        foreach ($steps as $stepIndex => $step) {
            $component = $step['component'] ?? [];

            // Check direct buttons
            $buttons = $component['buttons'] ?? [];
            if (count($buttons) > 3) {
                $validator->errors()->add("steps.{$stepIndex}.component.buttons", "Máximo de 3 botões permitidos por componente.");
            }

            // Check action buttons
            $actionButtons = $component['action']['buttons'] ?? [];
            if (count($actionButtons) > 3) {
                $validator->errors()->add("steps.{$stepIndex}.component.action.buttons", "Máximo de 3 botões de ação permitidos por componente.");
            }

            // Validate button text length for WhatsApp
            foreach ($buttons as $buttonIndex => $button) {
                if (isset($button['text']) && strlen($button['text']) > 20) {
                    $validator->errors()->add("steps.{$stepIndex}.component.buttons.{$buttonIndex}.text", "Texto do botão muito longo para WhatsApp (máximo 20 caracteres).");
                }
            }

            foreach ($actionButtons as $buttonIndex => $button) {
                if (isset($button['text']) && strlen($button['text']) > 20) {
                    $validator->errors()->add("steps.{$stepIndex}.component.action.buttons.{$buttonIndex}.text", "Texto do botão de ação muito longo para WhatsApp (máximo 20 caracteres).");
                }
            }
        }
    }
}
