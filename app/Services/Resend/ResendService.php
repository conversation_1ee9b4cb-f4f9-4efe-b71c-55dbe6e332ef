<?php

namespace App\Services\Resend;

use App\Helpers\DBLog;
use App\Services\Resend\Contracts\EmailInterface;
use App\Services\Resend\Exceptions\ResendException;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\Response;

class ResendService
{
    private string $apiKey;
    private string $apiUrl;
    private int $timeout;
    private int $retryAttempts;
    private bool $sandboxMode;

    public function __construct()
    {
        $apiKey = config('resend.api_key');

        if (empty($apiKey)) {
            throw new ResendException('Resend API key is not configured');
        }

        $this->apiKey = $apiKey;
        $this->apiUrl = config('resend.api_url');
        $this->timeout = config('resend.timeout', 30);
        $this->retryAttempts = config('resend.retry_attempts', 3);
        $this->sandboxMode = config('resend.sandbox_mode', false);
    }

    /**
     * Send email using Resend API
     *
     * @param EmailInterface $email
     * @return array
     * @throws ResendException
     */
    public function send(EmailInterface $email): array
    {
        $payload = $email->toResendPayload();

        // Add sandbox mode if enabled
        if ($this->sandboxMode) {
            $payload['to'] = [['email' => '<EMAIL>', 'name' => 'Sandbox User']];
        }

        $this->logEmailAttempt($email, $payload);

        try {
            $response = $this->makeRequest('/emails', $payload);
            $result = $response->json();

            $this->logEmailSuccess($email, $result);

            return $result;
        } catch (ResendException $e) {
            $this->logEmailError($email, $e);
            throw $e;
        }
    }

    /**
     * Make HTTP request to Resend API
     *
     * @param string $endpoint
     * @param array $data
     * @return Response
     * @throws ResendException
     */
    private function makeRequest(string $endpoint, array $data): Response
    {
        $url = $this->apiUrl . $endpoint;

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])
            ->timeout($this->timeout)
            ->retry($this->retryAttempts, 1000)
            ->post($url, $data);

            if ($response->failed()) {
                $this->handleApiError($response);
            }

            return $response;
        } catch (\Illuminate\Http\Client\ConnectionException $e) {
            throw ResendException::networkError($e->getMessage(), [
                'url' => $url,
                'data' => $data
            ]);
        } catch (\Illuminate\Http\Client\RequestException $e) {
            // This handles cases where the response failed and threw an exception
            $response = $e->response;
            if ($response) {
                $this->handleApiError($response);
            } else {
                throw ResendException::networkError($e->getMessage(), [
                    'url' => $url,
                    'data' => $data
                ]);
            }
        }
    }

    /**
     * Handle API errors and throw appropriate exceptions
     *
     * @param Response $response
     * @throws ResendException
     */
    private function handleApiError(Response $response): void
    {
        $statusCode = $response->status();
        $body = $response->json();
        $message = $body['message'] ?? 'Unknown error';

        $context = [
            'status_code' => $statusCode,
            'response_body' => $body,
            'url' => $response->effectiveUri()
        ];

        switch ($statusCode) {
            case 401:
                throw ResendException::authenticationFailed($context);
            case 400:
                throw ResendException::badRequest($message, $context);
            case 429:
                throw ResendException::rateLimitExceeded($context);
            case 500:
            case 502:
            case 503:
                throw ResendException::serverError($message, $context);
            default:
                throw new ResendException("HTTP {$statusCode}: {$message}", $statusCode, null, $context);
        }
    }

    /**
     * Log email sending attempt
     */
    private function logEmailAttempt(EmailInterface $email, array $payload): void
    {
        DBLog::log(
            "Resend email sending attempt",
            "ResendService",
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            [
                'to' => $email->getTo(),
                'subject' => $email->getSubject(),
                'template' => $email->getTemplatePath(),
                'tags' => $email->getTags(),
                'sandbox_mode' => $this->sandboxMode
            ]
        );
    }

    /**
     * Log successful email sending
     */
    private function logEmailSuccess(EmailInterface $email, array $result): void
    {
        DBLog::log(
            "Resend email sent successfully",
            "ResendService",
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            [
                'to' => $email->getTo(),
                'subject' => $email->getSubject(),
                'resend_id' => $result['id'] ?? null,
                'result' => $result
            ]
        );
    }

    /**
     * Log email sending error
     */
    private function logEmailError(EmailInterface $email, ResendException $exception): void
    {
        DBLog::logError(
            "Resend email sending failed: " . $exception->getMessage(),
            "ResendService",
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            [
                'to' => $email->getTo(),
                'subject' => $email->getSubject(),
                'error_code' => $exception->getCode(),
                'context' => $exception->getContext()
            ]
        );
    }
}
