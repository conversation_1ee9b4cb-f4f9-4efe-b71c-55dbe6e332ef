<?php

namespace App\Services\Meta\WhatsApp\Builders;

use App\Domains\ChatBot\Button;
use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\InteractiveMessage;
use App\Domains\ChatBot\ListSection;
use App\Domains\ChatBot\Template;
use App\Enums\ChatBot\InteractiveType;
use App\Enums\ChatBot\WhatsAppButtonType;
use InvalidArgumentException;

/**
 * WhatsApp Message Builder
 *
 * Specialized builder for constructing WhatsApp message payloads compliant with the official API.
 * Provides methods for building different types of WhatsApp messages with proper validation.
 *
 * @package App\Services\Meta\WhatsApp\Builders
 * @see https://developers.facebook.com/docs/whatsapp/cloud-api/reference/messages
 */
class WhatsAppMessageBuilder
{
    /**
     * WhatsApp API version
     */
    private const API_VERSION = 'v18.0';

    /**
     * Maximum text length for different message types
     */
    private const MAX_TEXT_LENGTH = 4096;
    private const MAX_HEADER_LENGTH = 60;
    private const MAX_FOOTER_LENGTH = 60;
    private const MAX_BUTTON_TEXT_LENGTH = 20;
    private const MAX_LIST_BUTTON_TEXT_LENGTH = 20;

    /**
     * Maximum number of items for interactive messages
     */
    private const MAX_BUTTONS = 3;
    private const MAX_LIST_SECTIONS = 10;
    private const MAX_LIST_ROWS_PER_SECTION = 10;

    /**
     * Build a simple text message payload
     *
     * @param string $to Recipient phone number in international format
     * @param string $text Message text content
     * @param bool $previewUrl Whether to show URL preview (default: false)
     * @return array WhatsApp API compliant payload
     * @throws InvalidArgumentException
     */
    public function buildTextMessage(string $to, string $text, bool $previewUrl = false): array
    {
        $this->validatePhoneNumber($to);
        $this->validateTextLength($text, self::MAX_TEXT_LENGTH, 'Message text');

        return [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'text',
            'text' => [
                'body' => $text,
                'preview_url' => $previewUrl
            ]
        ];
    }

    /**
     * Build an interactive button message payload
     *
     * @param string $to Recipient phone number in international format
     * @param string $body Message body text
     * @param Button[] $buttons Array of Button objects (max 3)
     * @param string|null $header Optional header text
     * @param string|null $footer Optional footer text
     * @return array WhatsApp API compliant payload
     * @throws InvalidArgumentException
     */
    public function buildInteractiveButtonMessage(
        string $to,
        string $body,
        array $buttons,
        ?string $header = null,
        ?string $footer = null
    ): array {
        $this->validatePhoneNumber($to);
        $this->validateTextLength($body, self::MAX_TEXT_LENGTH, 'Message body');
        $this->validateButtons($buttons);

        if ($header !== null) {
            $this->validateTextLength($header, self::MAX_HEADER_LENGTH, 'Header');
        }

        if ($footer !== null) {
            $this->validateTextLength($footer, self::MAX_FOOTER_LENGTH, 'Footer');
        }

        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'interactive',
            'interactive' => [
                'type' => 'button',
                'body' => [
                    'text' => $body
                ],
                'action' => [
                    'buttons' => array_map(fn(Button $button) => $button->toWhatsAppPayload(), $buttons)
                ]
            ]
        ];

        if ($header !== null) {
            $payload['interactive']['header'] = [
                'type' => 'text',
                'text' => $header
            ];
        }

        if ($footer !== null) {
            $payload['interactive']['footer'] = [
                'text' => $footer
            ];
        }

        return $payload;
    }

    /**
     * Build an interactive list message payload
     *
     * @param string $to Recipient phone number in international format
     * @param string $body Message body text
     * @param ListSection[] $sections Array of ListSection objects
     * @param string $buttonText Text for the list button (default: "Ver opções")
     * @param string|null $header Optional header text
     * @param string|null $footer Optional footer text
     * @return array WhatsApp API compliant payload
     * @throws InvalidArgumentException
     */
    public function buildInteractiveListMessage(
        string $to,
        string $body,
        array $sections,
        string $buttonText = 'Ver opções',
        ?string $header = null,
        ?string $footer = null
    ): array {
        $this->validatePhoneNumber($to);
        $this->validateTextLength($body, self::MAX_TEXT_LENGTH, 'Message body');
        $this->validateTextLength($buttonText, self::MAX_LIST_BUTTON_TEXT_LENGTH, 'Button text');
        $this->validateListSections($sections);

        if ($header !== null) {
            $this->validateTextLength($header, self::MAX_HEADER_LENGTH, 'Header');
        }

        if ($footer !== null) {
            $this->validateTextLength($footer, self::MAX_FOOTER_LENGTH, 'Footer');
        }

        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'interactive',
            'interactive' => [
                'type' => 'list',
                'body' => [
                    'text' => $body
                ],
                'action' => [
                    'button' => $buttonText,
                    'sections' => array_map(fn(ListSection $section) => $section->toWhatsAppPayload(), $sections)
                ]
            ]
        ];

        if ($header !== null) {
            $payload['interactive']['header'] = [
                'type' => 'text',
                'text' => $header
            ];
        }

        if ($footer !== null) {
            $payload['interactive']['footer'] = [
                'text' => $footer
            ];
        }

        return $payload;
    }

    /**
     * Build a template message payload
     *
     * @param string $to Recipient phone number in international format
     * @param Template $template Template object with components
     * @param array $availableModels Available models for variable substitution
     * @return array WhatsApp API compliant payload
     * @throws InvalidArgumentException
     */
    public function buildTemplateMessage(string $to, Template $template, array $availableModels = []): array
    {
        $this->validatePhoneNumber($to);
        $this->validateTemplate($template);

        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'template',
            'template' => [
                'name' => $template->name,
                'language' => [
                    'code' => $template->language
                ]
            ]
        ];

        // Add components if they exist and have parameters
        $components = [];
        if ($template->components) {
            foreach ($template->components as $component) {
                $componentPayload = $component->toWhatsAppMessagePayload($availableModels);
                if ($componentPayload) {
                    $components[] = $componentPayload;
                }
            }
        }

        if (!empty($components)) {
            $payload['template']['components'] = $components;
        }

        return $payload;
    }

    /**
     * Validate phone number format
     *
     * @param string $phoneNumber
     * @throws InvalidArgumentException
     */
    private function validatePhoneNumber(string $phoneNumber): void
    {
        if (empty($phoneNumber)) {
            throw new InvalidArgumentException('Phone number is required');
        }

        // Remove any non-digit characters for validation
        $cleanNumber = preg_replace('/[^0-9]/', '', $phoneNumber);

        if (strlen($cleanNumber) < 10 || strlen($cleanNumber) > 15) {
            throw new InvalidArgumentException('Phone number must be between 10 and 15 digits');
        }
    }

    /**
     * Validate text length
     *
     * @param string $text
     * @param int $maxLength
     * @param string $fieldName
     * @throws InvalidArgumentException
     */
    private function validateTextLength(string $text, int $maxLength, string $fieldName): void
    {
        if (empty($text)) {
            throw new InvalidArgumentException("{$fieldName} cannot be empty");
        }

        if (mb_strlen($text) > $maxLength) {
            throw new InvalidArgumentException("{$fieldName} exceeds maximum length of {$maxLength} characters");
        }
    }

    /**
     * Validate buttons for interactive button message
     *
     * @param Button[] $buttons
     * @throws InvalidArgumentException
     */
    private function validateButtons(array $buttons): void
    {
        if (empty($buttons)) {
            throw new InvalidArgumentException('At least one button is required');
        }

        if (count($buttons) > self::MAX_BUTTONS) {
            throw new InvalidArgumentException('Maximum of ' . self::MAX_BUTTONS . ' buttons allowed');
        }

        foreach ($buttons as $button) {
            if (!($button instanceof Button)) {
                throw new InvalidArgumentException('All buttons must be Button instances');
            }

            if (!$button->validateForWhatsApp()) {
                throw new InvalidArgumentException('Button validation failed: ' . ($button->text ?? 'Unknown button'));
            }
        }

        // Validate button collection compliance
        Button::validateButtonCollection($buttons);
    }

    /**
     * Validate list sections for interactive list message
     *
     * @param ListSection[] $sections
     * @throws InvalidArgumentException
     */
    private function validateListSections(array $sections): void
    {
        if (empty($sections)) {
            throw new InvalidArgumentException('At least one section is required');
        }

        if (count($sections) > self::MAX_LIST_SECTIONS) {
            throw new InvalidArgumentException('Maximum of ' . self::MAX_LIST_SECTIONS . ' sections allowed');
        }

        $totalRows = 0;
        foreach ($sections as $section) {
            if (!($section instanceof ListSection)) {
                throw new InvalidArgumentException('All sections must be ListSection instances');
            }

            $rowCount = count($section->rows ?? []);
            if ($rowCount > self::MAX_LIST_ROWS_PER_SECTION) {
                throw new InvalidArgumentException('Maximum of ' . self::MAX_LIST_ROWS_PER_SECTION . ' rows per section allowed');
            }

            $totalRows += $rowCount;
        }

        if ($totalRows > self::MAX_LIST_SECTIONS) {
            throw new InvalidArgumentException('Maximum of ' . self::MAX_LIST_SECTIONS . ' total rows allowed across all sections');
        }
    }

    /**
     * Validate template for template message
     *
     * @param Template $template
     * @throws InvalidArgumentException
     */
    private function validateTemplate(Template $template): void
    {
        if (empty($template->name)) {
            throw new InvalidArgumentException('Template name is required');
        }

        if (empty($template->language)) {
            throw new InvalidArgumentException('Template language is required');
        }

        if (!$template->isWhatsAppPublished()) {
            throw new InvalidArgumentException('Template must be published on WhatsApp');
        }

        // Validate template name format (alphanumeric, underscores, lowercase)
        if (!preg_match('/^[a-z0-9_]+$/', $template->name)) {
            throw new InvalidArgumentException('Template name must contain only lowercase letters, numbers, and underscores');
        }

        // Validate language code format (ISO 639-1 with optional country code)
        if (!preg_match('/^[a-z]{2}(_[A-Z]{2})?$/', $template->language)) {
            throw new InvalidArgumentException('Template language must be a valid language code (e.g., en, pt_BR)');
        }
    }

    /**
     * Validate complete payload structure for WhatsApp API compliance
     *
     * @param array $payload
     * @return bool
     * @throws InvalidArgumentException
     */
    public function validatePayload(array $payload): bool
    {
        // Check required top-level fields
        $requiredFields = ['messaging_product', 'to', 'type'];
        foreach ($requiredFields as $field) {
            if (!isset($payload[$field])) {
                throw new InvalidArgumentException("Required field '{$field}' is missing from payload");
            }
        }

        // Validate messaging_product
        if ($payload['messaging_product'] !== 'whatsapp') {
            throw new InvalidArgumentException('messaging_product must be "whatsapp"');
        }

        // Validate phone number format
        $this->validatePhoneNumber($payload['to']);

        // Validate type-specific payload structure
        switch ($payload['type']) {
            case 'text':
                $this->validateTextPayload($payload);
                break;
            case 'interactive':
                $this->validateInteractivePayload($payload);
                break;
            case 'template':
                $this->validateTemplatePayload($payload);
                break;
            default:
                throw new InvalidArgumentException('Unsupported message type: ' . $payload['type']);
        }

        return true;
    }

    /**
     * Validate text message payload structure
     *
     * @param array $payload
     * @throws InvalidArgumentException
     */
    private function validateTextPayload(array $payload): void
    {
        if (!isset($payload['text']) || !is_array($payload['text'])) {
            throw new InvalidArgumentException('Text payload must contain a "text" object');
        }

        if (!isset($payload['text']['body']) || empty($payload['text']['body'])) {
            throw new InvalidArgumentException('Text message must have a non-empty body');
        }

        $this->validateTextLength($payload['text']['body'], self::MAX_TEXT_LENGTH, 'Text body');
    }

    /**
     * Validate interactive message payload structure
     *
     * @param array $payload
     * @throws InvalidArgumentException
     */
    private function validateInteractivePayload(array $payload): void
    {
        if (!isset($payload['interactive']) || !is_array($payload['interactive'])) {
            throw new InvalidArgumentException('Interactive payload must contain an "interactive" object');
        }

        $interactive = $payload['interactive'];

        if (!isset($interactive['type'])) {
            throw new InvalidArgumentException('Interactive message must have a type');
        }

        if (!in_array($interactive['type'], ['button', 'list'])) {
            throw new InvalidArgumentException('Interactive type must be "button" or "list"');
        }

        if (!isset($interactive['body']['text']) || empty($interactive['body']['text'])) {
            throw new InvalidArgumentException('Interactive message must have a non-empty body text');
        }

        $this->validateTextLength($interactive['body']['text'], self::MAX_TEXT_LENGTH, 'Interactive body');

        // Validate type-specific structure
        if ($interactive['type'] === 'button') {
            $this->validateInteractiveButtonPayload($interactive);
        } elseif ($interactive['type'] === 'list') {
            $this->validateInteractiveListPayload($interactive);
        }
    }

    /**
     * Validate interactive button payload structure
     *
     * @param array $interactive
     * @throws InvalidArgumentException
     */
    private function validateInteractiveButtonPayload(array $interactive): void
    {
        if (!isset($interactive['action']['buttons']) || !is_array($interactive['action']['buttons'])) {
            throw new InvalidArgumentException('Button interactive message must have buttons in action');
        }

        $buttons = $interactive['action']['buttons'];
        if (count($buttons) > self::MAX_BUTTONS) {
            throw new InvalidArgumentException('Maximum of ' . self::MAX_BUTTONS . ' buttons allowed');
        }

        foreach ($buttons as $button) {
            if (!isset($button['type']) || !isset($button['reply']['id']) || !isset($button['reply']['title'])) {
                throw new InvalidArgumentException('Each button must have type, reply.id, and reply.title');
            }
        }
    }

    /**
     * Validate interactive list payload structure
     *
     * @param array $interactive
     * @throws InvalidArgumentException
     */
    private function validateInteractiveListPayload(array $interactive): void
    {
        if (!isset($interactive['action']['sections']) || !is_array($interactive['action']['sections'])) {
            throw new InvalidArgumentException('List interactive message must have sections in action');
        }

        $sections = $interactive['action']['sections'];
        if (count($sections) > self::MAX_LIST_SECTIONS) {
            throw new InvalidArgumentException('Maximum of ' . self::MAX_LIST_SECTIONS . ' sections allowed');
        }

        $totalRows = 0;
        foreach ($sections as $section) {
            if (!isset($section['rows']) || !is_array($section['rows'])) {
                throw new InvalidArgumentException('Each section must have rows array');
            }

            $totalRows += count($section['rows']);
        }

        if ($totalRows > self::MAX_LIST_SECTIONS) {
            throw new InvalidArgumentException('Maximum of ' . self::MAX_LIST_SECTIONS . ' total rows allowed');
        }
    }

    /**
     * Validate template message payload structure
     *
     * @param array $payload
     * @throws InvalidArgumentException
     */
    private function validateTemplatePayload(array $payload): void
    {
        if (!isset($payload['template']) || !is_array($payload['template'])) {
            throw new InvalidArgumentException('Template payload must contain a "template" object');
        }

        $template = $payload['template'];

        if (!isset($template['name']) || empty($template['name'])) {
            throw new InvalidArgumentException('Template must have a non-empty name');
        }

        if (!isset($template['language']['code']) || empty($template['language']['code'])) {
            throw new InvalidArgumentException('Template must have a language code');
        }

        // Validate template name format
        if (!preg_match('/^[a-z0-9_]+$/', $template['name'])) {
            throw new InvalidArgumentException('Template name must contain only lowercase letters, numbers, and underscores');
        }

        // Validate language code format
        if (!preg_match('/^[a-z]{2}(_[A-Z]{2})?$/', $template['language']['code'])) {
            throw new InvalidArgumentException('Template language must be a valid language code (e.g., en, pt_BR)');
        }
    }
}
