<?php

namespace Tests\Feature\Http\Controllers\ChatBot;

use App\Domains\ChatBot\Flow;
use App\Enums\ChatBot\FlowStatus;
use App\Factories\ChatBot\FlowFactory;
use App\Http\Controllers\ChatBot\FlowController;
use App\Models\User;
use App\Models\Organization;
use App\UseCases\ChatBot\Flow\SaveFullFlow;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;
use Mockery;

class FlowControllerRefactoredTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private FlowController $controller;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->controller = app()->make(FlowController::class);
    }

    public function test_save_full_flow_with_valid_request()
    {
        // Arrange
        $this->actingAs($this->user);

        $flowData = [
            'flow' => [
                'name' => 'Test Flow Refactored',
                'description' => 'Test Description',
                'status' => 'draft',
                'is_default_flow' => false,
                'inactivity_minutes' => 60,
                'version' => '1.0'
            ],
            'steps' => [
                [
                    'step' => 'Initial Step',
                    'position' => 0,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'step_type' => 'message',
                    'component' => [
                        'format' => 'TEXT',
                        'text' => 'Welcome message'
                    ]
                ]
            ]
        ];

        // Mock the use case to return a flow
        $mockFlow = new Flow(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Test Flow Refactored',
            description: 'Test Description',
            steps_count: 1,
            json: json_encode($flowData),
            status: FlowStatus::DRAFT
        );

        $mockUseCase = Mockery::mock(SaveFullFlow::class);
        $mockUseCase->shouldReceive('perform')
            ->once()
            ->andReturn($mockFlow);

        $this->app->instance(SaveFullFlow::class, $mockUseCase);

        // Act
        $response = $this->postJson('/api/flow/save', $flowData);

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'message',
            'status',
            'data' => [
                'id',
                'name',
                'description',
                'organization_id',
                'steps_count'
            ],
            'errors',
            'pagination'
        ]);

        $response->assertJson([
            'message' => 'Flow updated successfully',
            'status' => 'success'
        ]);
    }

    public function test_save_full_flow_with_update_existing_flow()
    {
        // Arrange
        $this->actingAs($this->user);

        $flowData = [
            'flow' => [
                'name' => 'Updated Flow',
                'description' => 'Updated Description',
                'status' => 'active',
                'is_default_flow' => false,
                'inactivity_minutes' => 60,
                'version' => '1.0'
            ],
            'steps' => [
                [
                    'step' => 'Updated Step',
                    'position' => 0,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'step_type' => 'message',
                    'component' => [
                        'format' => 'TEXT',
                        'text' => 'Updated welcome message'
                    ]
                ]
            ]
        ];

        // Mock updated flow
        $mockFlow = new Flow(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Updated Flow',
            description: 'Updated Description',
            steps_count: 1,
            json: json_encode($flowData),
            status: FlowStatus::ACTIVE
        );

        $mockUseCase = Mockery::mock(SaveFullFlow::class);
        $mockUseCase->shouldReceive('perform')
            ->once()
            ->andReturn($mockFlow);

        $this->app->instance(SaveFullFlow::class, $mockUseCase);

        // Act
        $response = $this->postJson('/api/flow/save', $flowData);

        // Assert
        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Flow updated successfully',
            'status' => 'success'
        ]);
    }

    public function test_save_full_flow_unauthorized_without_organization()
    {
        // Arrange
        $userWithoutOrg = User::factory()->create(['organization_id' => null]);
        $this->actingAs($userWithoutOrg);

        $flowData = [
            'flow' => [
                'name' => 'Test Flow',
                'status' => 'draft',
                'is_default_flow' => false,
                'inactivity_minutes' => 60,
                'version' => '1.0'
            ],
            'steps' => [
                [
                    'step' => 'Test Step',
                    'position' => 0,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'step_type' => 'message'
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/flow/save', $flowData);

        // Assert
        $response->assertStatus(403);
        $response->assertJson([
            'message' => 'Unauthorized: Organization required',
            'status' => 'error'
        ]);
    }

    public function test_save_full_flow_validation_error()
    {
        // Arrange
        $this->actingAs($this->user);

        $invalidFlowData = [
            'flow' => [], // Missing required name
            'steps' => []
        ];

        // Act
        $response = $this->postJson('/api/flow/save', $invalidFlowData);

        // Assert
        $response->assertStatus(422);
        $response->assertJsonStructure([
            'message',
            'status',
            'errors',
            'data',
            'pagination'
        ]);
    }

    public function test_save_full_flow_logs_operation_start_and_success()
    {
        // Arrange
        Log::spy();
        $this->actingAs($this->user);

        $flowData = [
            'flow' => [
                'name' => 'Logged Flow',
                'description' => 'Test logging',
                'status' => 'draft',
                'is_default_flow' => false,
                'inactivity_minutes' => 60,
                'version' => '1.0'
            ],
            'steps' => [
                [
                    'step' => 'Test Step',
                    'position' => 0,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'step_type' => 'message'
                ]
            ]
        ];

        $mockFlow = new Flow(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Logged Flow',
            description: 'Test logging',
            steps_count: 1,
            json: json_encode($flowData),
            status: FlowStatus::DRAFT
        );

        $mockUseCase = Mockery::mock(SaveFullFlow::class);
        $mockUseCase->shouldReceive('perform')->andReturn($mockFlow);
        $this->app->instance(SaveFullFlow::class, $mockUseCase);

        // Act
        $response = $this->postJson('/api/flow/save', $flowData);

        // Assert
        $response->assertStatus(200);

        Log::shouldHaveReceived('info')
            ->with('SaveFullFlow operation started', Mockery::type('array'))
            ->once();

        Log::shouldHaveReceived('info')
            ->with('SaveFullFlow operation completed successfully', Mockery::type('array'))
            ->once();
    }

    public function test_save_full_flow_handles_database_exception()
    {
        // Arrange
        Log::spy();
        $this->actingAs($this->user);

        $flowData = [
            'flow' => [
                'name' => 'Test Flow',
                'status' => 'draft',
                'is_default_flow' => false,
                'inactivity_minutes' => 60,
                'version' => '1.0'
            ],
            'steps' => [
                [
                    'step' => 'Test Step',
                    'position' => 0,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'step_type' => 'message'
                ]
            ]
        ];

        $mockUseCase = Mockery::mock(SaveFullFlow::class);
        $mockUseCase->shouldReceive('perform')
            ->andThrow(new \Illuminate\Database\QueryException(
                'mysql',
                'SELECT * FROM flows',
                [],
                new \Exception('Database connection failed')
            ));

        $this->app->instance(SaveFullFlow::class, $mockUseCase);

        // Act
        $response = $this->postJson('/api/flow/save', $flowData);

        // Assert
        $response->assertStatus(500);
        $response->assertJson([
            'message' => 'Database error occurred while saving flow',
            'status' => 'error'
        ]);

        Log::shouldHaveReceived('error')
            ->with('SaveFullFlow database error', Mockery::type('array'))
            ->once();
    }

    public function test_save_full_flow_includes_execution_time_in_response()
    {
        // Arrange
        $this->actingAs($this->user);

        $flowData = [
            'flow' => [
                'name' => 'Performance Test Flow',
                'status' => 'draft',
                'is_default_flow' => false,
                'inactivity_minutes' => 60,
                'version' => '1.0'
            ],
            'steps' => [
                [
                    'step' => 'Test Step',
                    'position' => 0,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'step_type' => 'message'
                ]
            ]
        ];

        $mockFlow = new Flow(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Performance Test Flow',
            description: null,
            steps_count: 0,
            json: json_encode($flowData),
            status: FlowStatus::DRAFT
        );

        $mockUseCase = Mockery::mock(SaveFullFlow::class);
        $mockUseCase->shouldReceive('perform')->andReturn($mockFlow);
        $this->app->instance(SaveFullFlow::class, $mockUseCase);

        // Act
        $response = $this->postJson('/api/flow/save', $flowData);

        // Assert
        $response->assertStatus(200);
        // Check that execution time is logged (we can't test meta in response since trait doesn't support it)
        $this->assertTrue(true); // Placeholder - execution time is logged internally

        $executionTime = $response->json('pagination.execution_time_ms');
        $this->assertIsNumeric($executionTime);
        $this->assertGreaterThan(0, $executionTime);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
