<?php

namespace Tests\Feature\Http\Requests\Template;

use App\Http\Requests\Template\SaveFullTemplateRequest;
use App\Models\Organization;
use App\Models\PhoneNumber;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class SaveFullTemplateRequestTest extends TestCase
{
    use RefreshDatabase;

    private Organization $organization;
    private User $user;
    private PhoneNumber $phoneNumber;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        $this->phoneNumber = PhoneNumber::factory()->create(['organization_id' => $this->organization->id]);
    }

    public function test_valid_template_json_passes_validation()
    {
        $validData = [
            'template_json' => json_encode([
                'name' => 'test_template',
                'category' => 'UTILITY',
                'language' => 'en_US',
                'components' => [
                    [
                        'type' => 'HEADER',
                        'format' => 'TEXT',
                        'text' => 'Hello {{1}}'
                    ],
                    [
                        'type' => 'BODY',
                        'text' => 'This is a test message for {{1}}.'
                    ],
                    [
                        'type' => 'FOOTER',
                        'text' => 'Thank you'
                    ]
                ]
            ]),
            'phone_number_id' => $this->phoneNumber->id
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($validData, $request->rules());
        $request->withValidator($validator);

        if (!$validator->passes()) {
            dd($validator->errors()->toArray());
        }
        $this->assertTrue($validator->passes());
    }

    public function test_missing_template_json_fails_validation()
    {
        $invalidData = [
            'phone_number_id' => $this->phoneNumber->id
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('template_json', $validator->errors()->toArray());
    }

    public function test_invalid_template_name_fails_validation()
    {
        $invalidData = [
            'template_json' => json_encode([
                'name' => 'Invalid Template Name!', // Contains spaces and special characters
                'category' => 'UTILITY',
                'language' => 'en_US',
                'components' => [
                    [
                        'type' => 'BODY',
                        'text' => 'Test message'
                    ]
                ]
            ])
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('template_json.name', $validator->errors()->toArray());
    }

    public function test_invalid_category_fails_validation()
    {
        $invalidData = [
            'template_json' => json_encode([
                'name' => 'test_template',
                'category' => 'INVALID_CATEGORY',
                'language' => 'en_US',
                'components' => [
                    [
                        'type' => 'BODY',
                        'text' => 'Test message'
                    ]
                ]
            ])
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('template_json.category', $validator->errors()->toArray());
    }

    public function test_invalid_language_fails_validation()
    {
        $invalidData = [
            'template_json' => json_encode([
                'name' => 'test_template',
                'category' => 'UTILITY',
                'language' => 'invalid_lang',
                'components' => [
                    [
                        'type' => 'BODY',
                        'text' => 'Test message'
                    ]
                ]
            ])
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('template_json.language', $validator->errors()->toArray());
    }

    public function test_missing_body_component_fails_validation()
    {
        $invalidData = [
            'template_json' => json_encode([
                'name' => 'test_template',
                'category' => 'UTILITY',
                'language' => 'en_US',
                'components' => [
                    [
                        'type' => 'HEADER',
                        'format' => 'TEXT',
                        'text' => 'Header text'
                    ]
                ]
            ])
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($invalidData, $request->rules());
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('template_json.components', $validator->errors()->toArray());
    }

    public function test_too_many_buttons_fails_validation()
    {
        $buttons = [];
        for ($i = 1; $i <= 11; $i++) { // More than MAX_BUTTONS_PER_TEMPLATE (10)
            $buttons[] = [
                'type' => 'QUICK_REPLY',
                'text' => "Button {$i}"
            ];
        }

        $invalidData = [
            'template_json' => json_encode([
                'name' => 'test_template',
                'category' => 'UTILITY',
                'language' => 'en_US',
                'components' => [
                    [
                        'type' => 'BODY',
                        'text' => 'Test message'
                    ],
                    [
                        'type' => 'BUTTONS',
                        'buttons' => $buttons
                    ]
                ]
            ])
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
    }

    public function test_header_text_too_long_fails_validation()
    {
        $longText = str_repeat('a', 61); // More than MAX_HEADER_TEXT_LENGTH (60)

        $invalidData = [
            'template_json' => json_encode([
                'name' => 'test_template',
                'category' => 'UTILITY',
                'language' => 'en_US',
                'components' => [
                    [
                        'type' => 'HEADER',
                        'format' => 'TEXT',
                        'text' => $longText
                    ],
                    [
                        'type' => 'BODY',
                        'text' => 'Test message'
                    ]
                ]
            ])
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($invalidData, $request->rules());
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
    }

    public function test_body_text_too_long_fails_validation()
    {
        $longText = str_repeat('a', 1025); // More than MAX_BODY_TEXT_LENGTH (1024)

        $invalidData = [
            'template_json' => json_encode([
                'name' => 'test_template',
                'category' => 'UTILITY',
                'language' => 'en_US',
                'components' => [
                    [
                        'type' => 'BODY',
                        'text' => $longText
                    ]
                ]
            ])
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($invalidData, $request->rules());
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
    }

    public function test_body_text_with_newlines_fails_validation()
    {
        $invalidData = [
            'template_json' => json_encode([
                'name' => 'test_template',
                'category' => 'UTILITY',
                'language' => 'en_US',
                'components' => [
                    [
                        'type' => 'BODY',
                        'text' => "Line 1\nLine 2" // Contains newline
                    ]
                ]
            ])
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($invalidData, $request->rules());
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
    }

    public function test_invalid_button_url_fails_validation()
    {
        $invalidData = [
            'template_json' => json_encode([
                'name' => 'test_template',
                'category' => 'UTILITY',
                'language' => 'en_US',
                'components' => [
                    [
                        'type' => 'BODY',
                        'text' => 'Test message'
                    ],
                    [
                        'type' => 'BUTTONS',
                        'buttons' => [
                            [
                                'type' => 'URL',
                                'text' => 'Visit',
                                'url' => 'invalid-url' // Invalid URL format
                            ]
                        ]
                    ]
                ]
            ])
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
    }

    public function test_invalid_phone_number_format_fails_validation()
    {
        $invalidData = [
            'template_json' => json_encode([
                'name' => 'test_template',
                'category' => 'UTILITY',
                'language' => 'en_US',
                'components' => [
                    [
                        'type' => 'BODY',
                        'text' => 'Test message'
                    ],
                    [
                        'type' => 'BUTTONS',
                        'buttons' => [
                            [
                                'type' => 'PHONE_NUMBER',
                                'text' => 'Call',
                                'phone_number' => '123456789' // Missing + prefix
                            ]
                        ]
                    ]
                ]
            ])
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
    }

    public function test_non_sequential_placeholders_fail_validation()
    {
        $invalidData = [
            'template_json' => json_encode([
                'name' => 'test_template',
                'category' => 'UTILITY',
                'language' => 'en_US',
                'components' => [
                    [
                        'type' => 'BODY',
                        'text' => 'Hello {{1}} and {{3}}' // Missing {{2}}
                    ]
                ]
            ])
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($invalidData, $request->rules());
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
    }

    public function test_too_many_url_buttons_fails_validation()
    {
        $invalidData = [
            'template_json' => json_encode([
                'name' => 'test_template',
                'category' => 'UTILITY',
                'language' => 'en_US',
                'components' => [
                    [
                        'type' => 'BODY',
                        'text' => 'Test message'
                    ],
                    [
                        'type' => 'BUTTONS',
                        'buttons' => [
                            [
                                'type' => 'URL',
                                'text' => 'URL 1',
                                'url' => 'https://example1.com'
                            ],
                            [
                                'type' => 'URL',
                                'text' => 'URL 2',
                                'url' => 'https://example2.com'
                            ],
                            [
                                'type' => 'URL',
                                'text' => 'URL 3', // More than MAX_URL_BUTTONS (2)
                                'url' => 'https://example3.com'
                            ]
                        ]
                    ]
                ]
            ])
        ];

        $request = new SaveFullTemplateRequest();
        $validator = Validator::make($invalidData, $request->rules());
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
    }
}
