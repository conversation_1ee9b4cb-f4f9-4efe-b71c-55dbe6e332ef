<?php

namespace Tests\Feature\Http\Requests\Flow;

use App\Http\Requests\Flow\SaveFullFlowRequest;
use App\Services\ChatBot\FlowValidationService;
use App\Enums\ChatBot\FlowStatus;
use App\Enums\ChatBot\StepType;
use App\Enums\ChatBot\ComponentFormat;
use App\Enums\ChatBot\WhatsAppButtonType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class SaveFullFlowRequestIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private function getValidFlowData(): array
    {
        return [
            'flow' => [
                'name' => 'Integration Test Flow',
                'description' => 'Flow for integration testing',
                'is_default_flow' => false,
                'inactivity_minutes' => 60,
                'ending_conversation_message' => 'Goodbye!',
                'version' => '1.0',
                'status' => FlowStatus::DRAFT->value,
                'variables' => ['client' => ['name', 'email']]
            ],
            'steps' => [
                [
                    'step' => 'welcome',
                    'step_type' => StepType::MESSAGE->value,
                    'position' => 0,
                    'next_step' => 1,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'component' => [
                        'name' => 'Welcome Component',
                        'type' => 'message',
                        'text' => 'Welcome {{client.name}}!',
                        'format' => ComponentFormat::TEXT->value,
                        'buttons' => [
                            [
                                'text' => 'Continue',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'navigation',
                                'internal_data' => 'next_step'
                            ]
                        ]
                    ]
                ],
                [
                    'step' => 'end',
                    'step_type' => StepType::MESSAGE->value,
                    'position' => 1,
                    'is_initial_step' => false,
                    'is_ending_step' => true,
                    'component' => [
                        'name' => 'End Component',
                        'type' => 'message',
                        'text' => 'Thank you for using our service!',
                        'format' => ComponentFormat::TEXT->value
                    ]
                ]
            ]
        ];
    }

    public function test_valid_flow_passes_complete_validation()
    {
        $data = $this->getValidFlowData();

        $request = new SaveFullFlowRequest();
        $request->replace($data); // Set the data in the request
        $validator = Validator::make($data, $request->rules());

        // Apply custom validation logic
        $request->withValidator($validator);

        $this->assertTrue($validator->passes(), 'Validator should pass but failed with errors: ' . json_encode($validator->errors()->toArray()));
        $this->assertEmpty($validator->errors()->toArray());
    }

    public function test_flow_with_duplicate_positions_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][1]['position'] = 0; // Same as first step

        $request = new SaveFullFlowRequest();
        $request->replace($data);
        $validator = Validator::make($data, $request->rules());

        // Apply custom validation logic
        $request->withValidator($validator);

        $this->assertTrue($validator->fails());
        // The error should be in flow_structure, not steps.1.position
        $this->assertArrayHasKey('flow_structure', $validator->errors()->toArray());
    }

    public function test_flow_with_invalid_navigation_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['next_step'] = 999; // Invalid reference

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        // Apply custom validation logic
        $request->withValidator($validator);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('flow_structure', $validator->errors()->toArray());
    }

    public function test_flow_with_too_many_buttons_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['component']['buttons'] = [
            ['text' => 'Btn1', 'type' => WhatsAppButtonType::REPLY->value],
            ['text' => 'Btn2', 'type' => WhatsAppButtonType::REPLY->value],
            ['text' => 'Btn3', 'type' => WhatsAppButtonType::REPLY->value],
            ['text' => 'Btn4', 'type' => WhatsAppButtonType::REPLY->value], // 4 > 3 limit
        ];

        $request = new SaveFullFlowRequest();
        $request->replace($data);
        $validator = Validator::make($data, $request->rules());

        // Apply custom validation logic
        $request->withValidator($validator);

        $this->assertTrue($validator->fails());
        // The error should be in flow_structure from FlowValidationService
        $this->assertArrayHasKey('flow_structure', $validator->errors()->toArray());
    }

    public function test_flow_with_infinite_loop_fails_validation()
    {
        $data = $this->getValidFlowData();
        // Create infinite loop: step 0 -> step 1 -> step 0
        $data['steps'][0]['next_step'] = 1;
        $data['steps'][1]['next_step'] = 0;
        $data['steps'][1]['is_ending_step'] = false;

        $request = new SaveFullFlowRequest();
        $request->replace($data);
        $validator = Validator::make($data, $request->rules());

        // Apply custom validation logic
        $request->withValidator($validator);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('flow_structure', $validator->errors()->toArray());

        $errors = $validator->errors()->get('flow_structure');
        $this->assertTrue(
            collect($errors)->contains(function ($error) {
                return str_contains($error, 'Loop infinito detectado');
            })
        );
    }

    public function test_flow_without_initial_step_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['is_initial_step'] = false;

        $request = new SaveFullFlowRequest();
        $request->replace($data);
        $validator = Validator::make($data, $request->rules());

        // Apply custom validation logic
        $request->withValidator($validator);

        $this->assertTrue($validator->fails());
        // The error should be in flow_structure from FlowValidationService
        $this->assertArrayHasKey('flow_structure', $validator->errors()->toArray());
    }

    public function test_flow_without_ending_step_passes_with_warnings()
    {
        $data = $this->getValidFlowData();
        $data['steps'][1]['is_ending_step'] = false;

        $request = new SaveFullFlowRequest();
        $request->replace($data);
        $validator = Validator::make($data, $request->rules());

        // Apply custom validation logic
        $request->withValidator($validator);

        // Should pass validation but generate warnings
        $this->assertTrue($validator->passes());

        // Check if warnings were added to session
        $this->assertTrue(session()->has('flow_validation_warnings'));
        $warnings = session('flow_validation_warnings');
        $this->assertTrue(
            collect($warnings)->contains(function ($warning) {
                return str_contains($warning, 'não tem step final definido');
            })
        );
    }

    public function test_flow_with_action_buttons_passes_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['component']['action'] = [
            'buttons' => [
                [
                    'text' => 'Action',
                    'type' => WhatsAppButtonType::REPLY->value,
                    'internal_type' => 'action',
                    'internal_data' => 'action_data'
                ]
            ]
        ];

        $request = new SaveFullFlowRequest();
        $request->replace($data);
        $validator = Validator::make($data, $request->rules());

        // Apply custom validation logic
        $request->withValidator($validator);

        $this->assertTrue($validator->passes());
    }

    public function test_flow_with_parameters_passes_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['component']['parameters'] = [
            [
                'type' => 'text',
                'text' => '{{client.name}}',
                'index' => 0
            ],
            [
                'type' => 'text',
                'text' => '{{client.email}}',
                'index' => 1
            ]
        ];

        $request = new SaveFullFlowRequest();
        $request->replace($data);
        $validator = Validator::make($data, $request->rules());

        // Apply custom validation logic
        $request->withValidator($validator);

        $this->assertTrue($validator->passes());
    }

    public function test_flow_validation_service_integration()
    {
        $data = $this->getValidFlowData();

        // Test FlowValidationService directly
        $service = app()->make(FlowValidationService::class);
        $result = $service->validateFlowStructure($data);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);

        // Test through SaveFullFlowRequest
        $request = new SaveFullFlowRequest();
        $request->replace($data);
        $validator = Validator::make($data, $request->rules());
        $request->withValidator($validator);

        $this->assertTrue($validator->passes());
    }

    public function test_prepare_for_validation_sets_positions()
    {
        $data = $this->getValidFlowData();
        // Remove positions to test auto-assignment
        unset($data['steps'][0]['position']);
        unset($data['steps'][1]['position']);

        $request = new SaveFullFlowRequest();
        $request->replace($data);

        // Call prepareForValidation manually
        $reflection = new \ReflectionClass($request);
        $method = $reflection->getMethod('prepareForValidation');
        $method->setAccessible(true);
        $method->invoke($request);

        $steps = $request->input('steps');
        $this->assertEquals(0, $steps[0]['position']);
        $this->assertEquals(1, $steps[1]['position']);
    }

    public function test_custom_validation_messages_are_in_portuguese()
    {
        $data = $this->getValidFlowData();
        unset($data['flow']['name']); // Trigger validation error

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());

        $this->assertTrue($validator->fails());
        $errors = $validator->errors()->toArray();
        $this->assertArrayHasKey('flow.name', $errors);
        $this->assertStringContainsString('obrigatório', $errors['flow.name'][0]);
    }
}
