<?php

namespace Tests\Feature\UseCases\ChatBot\Flow;

use App\Domains\ChatBot\Flow;
use App\Enums\ChatBot\FlowStatus;
use App\Factories\ChatBot\FlowFactory;
use App\Http\Requests\Flow\SaveFullFlowRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\FlowRepository;
use App\Services\ChatBot\FlowValidationService;
use App\UseCases\ChatBot\Flow\SaveFullFlow;
use App\UseCases\ChatBot\Step\SaveFullStep;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Mockery;
use Tests\TestCase;

class SaveFullFlowRefactoredTest extends TestCase
{
    use RefreshDatabase;

    private Organization $organization;
    private User $user;
    private SaveFullFlow $useCase;
    private FlowRepository $flowRepository;
    private FlowFactory $flowFactory;
    private FlowValidationService $validationService;
    private SaveFullStep $saveFullStepUseCase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->flowRepository = Mockery::mock(FlowRepository::class);
        $this->flowFactory = Mockery::mock(FlowFactory::class);
        $this->validationService = Mockery::mock(FlowValidationService::class);
        $this->saveFullStepUseCase = Mockery::mock(SaveFullStep::class);

        $this->useCase = new SaveFullFlow(
            $this->flowRepository,
            $this->flowFactory,
            $this->validationService,
            $this->saveFullStepUseCase
        );

        $this->actingAs($this->user);
    }

    public function test_perform_creates_new_flow_successfully()
    {
        // Arrange
        $requestData = [
            'flow' => [
                'name' => 'Test Flow',
                'description' => 'Test Description',
                'status' => 'draft',
                'is_default_flow' => false,
                'inactivity_minutes' => 60,
                'version' => '1.0'
            ],
            'steps' => [
                [
                    'step' => 'Initial Step',
                    'position' => 0,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'step_type' => 'message',
                    'component' => [
                        'format' => 'TEXT',
                        'text' => 'Welcome message'
                    ]
                ]
            ]
        ];

        $request = Mockery::mock(SaveFullFlowRequest::class);
        $request->shouldReceive('validated')
            ->once()
            ->andReturn($requestData);

        $mockFlow = new Flow(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Test Flow',
            description: 'Test Description',
            steps_count: 1,
            json: json_encode($requestData),
            status: FlowStatus::DRAFT
        );

        // Mock validation service
        $this->validationService->shouldReceive('validateFlowStructure')
            ->once()
            ->andReturn([
                'valid' => true,
                'errors' => [],
                'warnings' => []
            ]);

        // Mock factory
        $this->flowFactory->shouldReceive('buildFromSaveFullFlow')
            ->once()
            ->andReturn($mockFlow);

        // Mock repository
        $this->flowRepository->shouldReceive('save')
            ->once()
            ->andReturn($mockFlow);

        // Mock step use case
        $this->saveFullStepUseCase->shouldReceive('perform')
            ->once()
            ->with($requestData['steps'][0], 0, $mockFlow);

        // Act
        $result = $this->useCase->perform($request);

        // Assert
        $this->assertInstanceOf(Flow::class, $result);
        $this->assertEquals('Test Flow', $result->name);
        $this->assertEquals(1, $result->steps_count);
    }

    public function test_perform_updates_existing_flow_successfully()
    {
        // Arrange
        $requestData = [
            'flow' => [
                'id' => 123,
                'name' => 'Updated Flow',
                'description' => 'Updated Description',
                'status' => 'active',
                'is_default_flow' => false,
                'inactivity_minutes' => 60,
                'version' => '1.0'
            ],
            'steps' => [
                [
                    'step' => 'Updated Step',
                    'position' => 0,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'step_type' => 'message',
                    'component' => [
                        'format' => 'TEXT',
                        'text' => 'Updated message'
                    ]
                ]
            ]
        ];

        $request = Mockery::mock(SaveFullFlowRequest::class);
        $request->shouldReceive('validated')
            ->once()
            ->andReturn($requestData);

        $mockFlow = new Flow(
            id: 123,
            organization_id: $this->organization->id,
            name: 'Updated Flow',
            description: 'Updated Description',
            steps_count: 1,
            json: json_encode($requestData),
            status: FlowStatus::ACTIVE
        );

        // Mock validation service
        $this->validationService->shouldReceive('validateFlowStructure')
            ->once()
            ->andReturn([
                'valid' => true,
                'errors' => [],
                'warnings' => ['Some warning']
            ]);

        // Mock factory
        $this->flowFactory->shouldReceive('buildFromSaveFullFlow')
            ->once()
            ->andReturn($mockFlow);

        // Mock repository
        $this->flowRepository->shouldReceive('save')
            ->once()
            ->andReturn($mockFlow);

        // Mock step use case
        $this->saveFullStepUseCase->shouldReceive('perform')
            ->once()
            ->with($requestData['steps'][0], 0, $mockFlow);

        // Act
        $result = $this->useCase->perform($request);

        // Assert
        $this->assertInstanceOf(Flow::class, $result);
        $this->assertEquals('Updated Flow', $result->name);
        $this->assertEquals(123, $result->id);
    }

    public function test_perform_throws_validation_exception_on_invalid_flow()
    {
        // Arrange
        $requestData = [
            'flow' => [
                'name' => 'Invalid Flow',
                'description' => 'Invalid Description'
            ],
            'steps' => []
        ];

        $request = Mockery::mock(SaveFullFlowRequest::class);
        $request->shouldReceive('validated')
            ->andReturn($requestData);

        // Mock validation service to return errors
        $this->validationService->shouldReceive('validateFlowStructure')
            ->once()
            ->andReturn([
                'valid' => false,
                'errors' => ['Flow deve ter pelo menos um step.'],
                'warnings' => []
            ]);

        // Expect exception
        $this->expectException(ValidationException::class);

        // Act
        $this->useCase->perform($request);
    }

    public function test_perform_handles_step_processing_exception()
    {
        // Arrange
        $requestData = [
            'flow' => [
                'name' => 'Test Flow',
                'description' => 'Test Description',
                'status' => 'draft'
            ],
            'steps' => [
                [
                    'step' => 'Failing Step',
                    'position' => 0,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'step_type' => 'message'
                ]
            ]
        ];

        $request = Mockery::mock(SaveFullFlowRequest::class);
        $request->shouldReceive('validated')
            ->andReturn($requestData);

        $mockFlow = new Flow(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Test Flow',
            description: 'Test Description',
            steps_count: 1,
            json: json_encode($requestData),
            status: FlowStatus::DRAFT
        );

        // Mock validation service
        $this->validationService->shouldReceive('validateFlowStructure')
            ->once()
            ->andReturn([
                'valid' => true,
                'errors' => [],
                'warnings' => []
            ]);

        // Mock factory
        $this->flowFactory->shouldReceive('buildFromSaveFullFlow')
            ->once()
            ->andReturn($mockFlow);

        // Mock repository
        $this->flowRepository->shouldReceive('save')
            ->once()
            ->andReturn($mockFlow);

        // Mock step use case to throw exception
        $this->saveFullStepUseCase->shouldReceive('perform')
            ->once()
            ->andThrow(new \Exception('Step processing failed'));

        // Expect exception
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Step processing failed');

        // Act
        $this->useCase->perform($request);
    }

    public function test_perform_logs_operation_start_and_completion()
    {
        // Arrange
        Log::spy();

        $requestData = [
            'flow' => [
                'name' => 'Test Flow',
                'description' => 'Test Description',
                'status' => 'draft'
            ],
            'steps' => [
                [
                    'step' => 'Test Step',
                    'position' => 0,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'step_type' => 'message',
                    'component' => [
                        'format' => 'TEXT',
                        'text' => 'Test message'
                    ]
                ]
            ]
        ];

        $request = Mockery::mock(SaveFullFlowRequest::class);
        $request->shouldReceive('validated')
            ->once()
            ->andReturn($requestData);

        $mockFlow = new Flow(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Test Flow',
            description: 'Test Description',
            steps_count: 1,
            json: json_encode($requestData),
            status: FlowStatus::DRAFT
        );

        // Mock all dependencies
        $this->validationService->shouldReceive('validateFlowStructure')
            ->once()
            ->andReturn(['valid' => true, 'errors' => [], 'warnings' => []]);

        $this->flowFactory->shouldReceive('buildFromSaveFullFlow')
            ->once()
            ->andReturn($mockFlow);

        $this->flowRepository->shouldReceive('save')
            ->once()
            ->andReturn($mockFlow);

        $this->saveFullStepUseCase->shouldReceive('perform')
            ->once();

        // Act
        $this->useCase->perform($request);

        // Assert
        Log::shouldHaveReceived('info')
            ->with('SaveFullFlow operation started', Mockery::type('array'))
            ->once();

        Log::shouldHaveReceived('info')
            ->with('SaveFullFlow operation completed successfully', Mockery::type('array'))
            ->once();

        // Ensure we have assertions
        $this->assertTrue(true);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
