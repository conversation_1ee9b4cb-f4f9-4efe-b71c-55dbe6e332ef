<?php

namespace Tests\Feature\Factories\ChatBot;

use App\Domains\ChatBot\Flow;
use App\Enums\ChatBot\FlowStatus;
use App\Factories\ChatBot\FlowFactory;
use App\Factories\ChatBot\StepFactory;
use Tests\TestCase;

class FlowFactoryOptimizedTest extends TestCase
{
    private FlowFactory $flowFactory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->flowFactory = app()->make(FlowFactory::class);
    }

    /** @test */
    public function buildFromSaveFullFlow_creates_flow_with_valid_data()
    {
        // Arrange
        $flowData = [
            'name' => 'Test Flow',
            'description' => 'Test Description',
            'is_default_flow' => true,
            'inactivity_minutes' => 120,
            'ending_conversation_message' => 'Goodbye',
            'version' => '2.0',
            'status' => 'active',
            'variables' => ['key' => 'value']
        ];
        $json = json_encode(['flow' => $flowData, 'steps' => []]);
        $stepsCount = 5;
        $organizationId = 1;
        $flowId = 123;

        // Act
        $flow = $this->flowFactory->buildFromSaveFullFlow(
            $flowData,
            $json,
            $stepsCount,
            $organizationId,
            $flowId
        );

        // Assert
        $this->assertInstanceOf(Flow::class, $flow);
        $this->assertEquals($flowId, $flow->id);
        $this->assertEquals($organizationId, $flow->organization_id);
        $this->assertEquals('Test Flow', $flow->name);
        $this->assertEquals('Test Description', $flow->description);
        $this->assertEquals($stepsCount, $flow->steps_count);
        $this->assertEquals($json, $flow->json);
        $this->assertTrue($flow->is_default_flow);
        $this->assertEquals(120, $flow->inactivity_minutes);
        $this->assertEquals('Goodbye', $flow->ending_conversation_message);
        $this->assertEquals('2.0', $flow->version);
        $this->assertEquals(FlowStatus::ACTIVE, $flow->status);
        $this->assertEquals(['key' => 'value'], $flow->variables);
    }

    /** @test */
    public function buildFromSaveFullFlow_applies_default_values()
    {
        // Arrange
        $flowData = [
            'name' => 'Minimal Flow'
        ];
        $json = json_encode(['flow' => $flowData, 'steps' => []]);
        $organizationId = 1;

        // Act
        $flow = $this->flowFactory->buildFromSaveFullFlow(
            $flowData,
            $json,
            null,
            $organizationId,
            null
        );

        // Assert
        $this->assertNull($flow->id);
        $this->assertEquals($organizationId, $flow->organization_id);
        $this->assertEquals('Minimal Flow', $flow->name);
        $this->assertNull($flow->description);
        $this->assertNull($flow->steps_count);
        $this->assertFalse($flow->is_default_flow);
        $this->assertEquals(60, $flow->inactivity_minutes);
        $this->assertNull($flow->ending_conversation_message);
        $this->assertEquals('1.0', $flow->version);
        $this->assertEquals(FlowStatus::DRAFT, $flow->status);
        $this->assertNull($flow->variables);
    }

    /** @test */
    public function buildFromSaveFullFlow_throws_exception_for_missing_name()
    {
        // Arrange
        $flowData = [
            'description' => 'Flow without name'
        ];
        $json = json_encode(['flow' => $flowData, 'steps' => []]);
        $organizationId = 1;

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Flow name is required');

        $this->flowFactory->buildFromSaveFullFlow(
            $flowData,
            $json,
            null,
            $organizationId,
            null
        );
    }

    /** @test */
    public function buildFromSaveFullFlow_throws_exception_for_missing_organization_id()
    {
        // Arrange
        $flowData = [
            'name' => 'Test Flow'
        ];
        $json = json_encode(['flow' => $flowData, 'steps' => []]);

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Organization ID is required');

        $this->flowFactory->buildFromSaveFullFlow(
            $flowData,
            $json,
            null,
            null,
            null
        );
    }

    /** @test */
    public function buildFromSaveFullFlow_throws_exception_for_invalid_status()
    {
        // Arrange
        $flowData = [
            'name' => 'Test Flow',
            'status' => 'invalid_status'
        ];
        $json = json_encode(['flow' => $flowData, 'steps' => []]);
        $organizationId = 1;

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid flow status: invalid_status');

        $this->flowFactory->buildFromSaveFullFlow(
            $flowData,
            $json,
            null,
            $organizationId,
            null
        );
    }

    /** @test */
    public function buildFromSaveFullFlow_throws_exception_for_invalid_variables()
    {
        // Arrange
        $flowData = [
            'name' => 'Test Flow',
            'variables' => 'invalid_variables_string'
        ];
        $json = json_encode(['flow' => $flowData, 'steps' => []]);
        $organizationId = 1;

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Flow variables must be an array');

        $this->flowFactory->buildFromSaveFullFlow(
            $flowData,
            $json,
            null,
            $organizationId,
            null
        );
    }

    /** @test */
    public function buildFromSaveFullFlow_throws_exception_for_invalid_inactivity_minutes()
    {
        // Arrange
        $flowData = [
            'name' => 'Test Flow',
            'inactivity_minutes' => 2000 // Above maximum
        ];
        $json = json_encode(['flow' => $flowData, 'steps' => []]);
        $organizationId = 1;

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Inactivity minutes must be between 1 and 1440');

        $this->flowFactory->buildFromSaveFullFlow(
            $flowData,
            $json,
            null,
            $organizationId,
            null
        );
    }

    /** @test */
    public function buildFromSaveFullFlow_trims_string_values()
    {
        // Arrange
        $flowData = [
            'name' => '  Test Flow  ',
            'description' => '  Test Description  ',
            'ending_conversation_message' => '  Goodbye  '
        ];
        $json = json_encode(['flow' => $flowData, 'steps' => []]);
        $organizationId = 1;

        // Act
        $flow = $this->flowFactory->buildFromSaveFullFlow(
            $flowData,
            $json,
            null,
            $organizationId,
            null
        );

        // Assert
        $this->assertEquals('Test Flow', $flow->name);
        $this->assertEquals('Test Description', $flow->description);
        $this->assertEquals('Goodbye', $flow->ending_conversation_message);
    }

    /** @test */
    public function factory_can_be_instantiated_via_app_make()
    {
        // Act & Assert
        $factory = app()->make(FlowFactory::class);
        $this->assertInstanceOf(FlowFactory::class, $factory);
    }
}
