<?php

namespace Tests\Feature\Repositories;

use App\Domains\ChatBot\Flow;
use App\Enums\ChatBot\FlowStatus;
use App\Factories\ChatBot\FlowFactory;
use App\Models\Flow as FlowModel;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\FlowRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class FlowRepositoryOptimizedTest extends TestCase
{
    use RefreshDatabase;

    private FlowRepository $flowRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->flowRepository = app()->make(FlowRepository::class);
        
        // Create test organization and user
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        
        $this->actingAs($this->user);
    }

    /** @test */
    public function store_creates_new_flow_successfully()
    {
        // Arrange
        $flowDomain = new Flow(
            null,
            $this->organization->id,
            'Test Flow',
            'Test Description',
            3,
            '{"flow": {"name": "Test Flow"}, "steps": []}',
            false,
            60,
            'Goodbye',
            '1.0',
            FlowStatus::DRAFT,
            ['key' => 'value']
        );

        // Act
        $result = $this->flowRepository->store($flowDomain);

        // Assert
        $this->assertInstanceOf(Flow::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Test Flow', $result->name);
        
        // Verify database
        $this->assertDatabaseHas('flows', [
            'id' => $result->id,
            'organization_id' => $this->organization->id,
            'name' => 'Test Flow'
        ]);
    }

    /** @test */
    public function store_throws_exception_for_missing_organization_id()
    {
        // Arrange
        $flowDomain = new Flow(
            null,
            null, // Missing organization_id
            'Test Flow',
            'Test Description',
            3,
            '{"flow": {"name": "Test Flow"}, "steps": []}',
            false,
            60,
            'Goodbye',
            '1.0',
            FlowStatus::DRAFT,
            null
        );

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Organization ID is required for flow creation');

        $this->flowRepository->store($flowDomain);
    }

    /** @test */
    public function store_throws_exception_for_missing_name()
    {
        // Arrange
        $flowDomain = new Flow(
            null,
            $this->organization->id,
            '', // Empty name
            'Test Description',
            3,
            '{"flow": {"name": ""}, "steps": []}',
            false,
            60,
            'Goodbye',
            '1.0',
            FlowStatus::DRAFT,
            null
        );

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Flow name is required');

        $this->flowRepository->store($flowDomain);
    }

    /** @test */
    public function update_modifies_existing_flow_successfully()
    {
        // Arrange
        $flowModel = FlowModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Original Name'
        ]);

        $flowDomain = new Flow(
            $flowModel->id,
            $this->organization->id,
            'Updated Name',
            'Updated Description',
            5,
            '{"flow": {"name": "Updated Name"}, "steps": []}',
            true,
            120,
            'Updated Goodbye',
            '2.0',
            FlowStatus::ACTIVE,
            ['updated' => 'value']
        );

        // Act
        $result = $this->flowRepository->update($flowDomain, $this->organization->id);

        // Assert
        $this->assertInstanceOf(Flow::class, $result);
        $this->assertEquals($flowModel->id, $result->id);
        $this->assertEquals('Updated Name', $result->name);
        
        // Verify database
        $this->assertDatabaseHas('flows', [
            'id' => $flowModel->id,
            'name' => 'Updated Name',
            'description' => 'Updated Description'
        ]);
    }

    /** @test */
    public function update_throws_exception_for_missing_flow_id()
    {
        // Arrange
        $flowDomain = new Flow(
            null, // Missing ID
            $this->organization->id,
            'Test Flow',
            'Test Description',
            3,
            '{"flow": {"name": "Test Flow"}, "steps": []}',
            false,
            60,
            'Goodbye',
            '1.0',
            FlowStatus::DRAFT,
            null
        );

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Flow ID is required for update');

        $this->flowRepository->update($flowDomain, $this->organization->id);
    }

    /** @test */
    public function update_throws_exception_for_non_existent_flow()
    {
        // Arrange
        $flowDomain = new Flow(
            999, // Non-existent ID
            $this->organization->id,
            'Test Flow',
            'Test Description',
            3,
            '{"flow": {"name": "Test Flow"}, "steps": []}',
            false,
            60,
            'Goodbye',
            '1.0',
            FlowStatus::DRAFT,
            null
        );

        // Act & Assert
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Flow 999 not found or does not belong to organization');

        $this->flowRepository->update($flowDomain, $this->organization->id);
    }

    /** @test */
    public function save_creates_new_flow_when_id_is_null()
    {
        // Arrange
        $flowDomain = new Flow(
            null,
            $this->organization->id,
            'New Flow',
            'New Description',
            2,
            '{"flow": {"name": "New Flow"}, "steps": []}',
            false,
            60,
            'Goodbye',
            '1.0',
            FlowStatus::DRAFT,
            null
        );

        // Act
        $result = $this->flowRepository->save($flowDomain, $this->organization->id);

        // Assert
        $this->assertInstanceOf(Flow::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals('New Flow', $result->name);
        
        // Verify database
        $this->assertDatabaseHas('flows', [
            'id' => $result->id,
            'name' => 'New Flow'
        ]);
    }

    /** @test */
    public function save_updates_existing_flow_when_id_is_provided()
    {
        // Arrange
        $flowModel = FlowModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Original Name'
        ]);

        $flowDomain = new Flow(
            $flowModel->id,
            $this->organization->id,
            'Updated Name',
            'Updated Description',
            3,
            '{"flow": {"name": "Updated Name"}, "steps": []}',
            false,
            60,
            'Goodbye',
            '1.0',
            FlowStatus::DRAFT,
            null
        );

        // Act
        $result = $this->flowRepository->save($flowDomain, $this->organization->id);

        // Assert
        $this->assertInstanceOf(Flow::class, $result);
        $this->assertEquals($flowModel->id, $result->id);
        $this->assertEquals('Updated Name', $result->name);
        
        // Verify database
        $this->assertDatabaseHas('flows', [
            'id' => $flowModel->id,
            'name' => 'Updated Name'
        ]);
    }

    /** @test */
    public function save_uses_database_transaction()
    {
        // Arrange
        $flowDomain = new Flow(
            null,
            $this->organization->id,
            'Transaction Test',
            'Test Description',
            1,
            '{"flow": {"name": "Transaction Test"}, "steps": []}',
            false,
            60,
            'Goodbye',
            '1.0',
            FlowStatus::DRAFT,
            null
        );

        // Mock DB transaction to verify it's called
        DB::shouldReceive('transaction')
            ->once()
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        // Act
        $result = $this->flowRepository->save($flowDomain, $this->organization->id);

        // Assert
        $this->assertInstanceOf(Flow::class, $result);
    }

    /** @test */
    public function repository_can_be_instantiated_via_app_make()
    {
        // Act & Assert
        $repository = app()->make(FlowRepository::class);
        $this->assertInstanceOf(FlowRepository::class, $repository);
    }
}
