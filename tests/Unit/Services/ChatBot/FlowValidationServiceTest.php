<?php

namespace Tests\Unit\Services\ChatBot;

use App\Services\ChatBot\FlowValidationService;
use App\Enums\ChatBot\StepType;
use Tests\TestCase;

class FlowValidationServiceTest extends TestCase
{
    private FlowValidationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new FlowValidationService();
    }

    private function getValidFlowData(): array
    {
        return [
            'flow' => [
                'name' => 'Test Flow',
                'description' => 'Test description'
            ],
            'steps' => [
                [
                    'step' => 'welcome',
                    'step_type' => StepType::MESSAGE->value,
                    'position' => 0,
                    'next_step' => 1,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'component' => [
                        'text' => 'Welcome message',
                        'buttons' => [
                            ['text' => 'Continue', 'type' => 'reply']
                        ]
                    ]
                ],
                [
                    'step' => 'end',
                    'step_type' => StepType::MESSAGE->value,
                    'position' => 1,
                    'is_initial_step' => false,
                    'is_ending_step' => true,
                    'component' => [
                        'text' => 'Thank you!'
                    ]
                ]
            ]
        ];
    }

    public function test_valid_flow_structure_passes_validation()
    {
        $flowData = $this->getValidFlowData();
        
        $result = $this->service->validateFlowStructure($flowData);

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
    }

    public function test_flow_without_name_fails_validation()
    {
        $flowData = $this->getValidFlowData();
        unset($flowData['flow']['name']);

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertFalse($result['valid']);
        $this->assertContains('Flow deve ter um nome.', $result['errors']);
    }

    public function test_flow_without_steps_fails_validation()
    {
        $flowData = $this->getValidFlowData();
        $flowData['steps'] = [];

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertFalse($result['valid']);
        $this->assertContains('Flow deve ter pelo menos um step.', $result['errors']);
    }

    public function test_flow_without_initial_step_fails_validation()
    {
        $flowData = $this->getValidFlowData();
        $flowData['steps'][0]['is_initial_step'] = false;

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertFalse($result['valid']);
        $this->assertContains('Flow deve ter pelo menos um step inicial.', $result['errors']);
    }

    public function test_flow_without_ending_step_generates_warning()
    {
        $flowData = $this->getValidFlowData();
        $flowData['steps'][1]['is_ending_step'] = false;

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertTrue($result['valid']); // Should still be valid
        $this->assertContains('Flow não tem step final definido. Considere adicionar um.', $result['warnings']);
    }

    public function test_duplicate_step_positions_fail_validation()
    {
        $flowData = $this->getValidFlowData();
        $flowData['steps'][1]['position'] = 0; // Same as first step

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertFalse($result['valid']);
        $this->assertTrue(
            collect($result['errors'])->contains(function ($error) {
                return str_contains($error, 'posição duplicada');
            })
        );
    }

    public function test_duplicate_step_identifiers_fail_validation()
    {
        $flowData = $this->getValidFlowData();
        $flowData['steps'][1]['step'] = 'welcome'; // Same as first step

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertFalse($result['valid']);
        $this->assertContains('Identificador de step duplicado: \'welcome\'', $result['errors']);
    }

    public function test_invalid_next_step_reference_fails_validation()
    {
        $flowData = $this->getValidFlowData();
        $flowData['steps'][0]['next_step'] = 999; // Non-existent position

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertFalse($result['valid']);
        $this->assertTrue(
            collect($result['errors'])->contains(function ($error) {
                return str_contains($error, 'referencia next_step inválido: 999');
            })
        );
    }

    public function test_invalid_earlier_step_reference_fails_validation()
    {
        $flowData = $this->getValidFlowData();
        $flowData['steps'][1]['earlier_step'] = 999; // Non-existent position

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertFalse($result['valid']);
        $this->assertTrue(
            collect($result['errors'])->contains(function ($error) {
                return str_contains($error, 'referencia earlier_step inválido: 999');
            })
        );
    }

    public function test_invalid_step_type_fails_validation()
    {
        $flowData = $this->getValidFlowData();
        $flowData['steps'][0]['step_type'] = 'invalid_type';

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertFalse($result['valid']);
        $this->assertTrue(
            collect($result['errors'])->contains(function ($error) {
                return str_contains($error, 'tem tipo inválido: invalid_type');
            })
        );
    }

    public function test_too_many_buttons_fails_validation()
    {
        $flowData = $this->getValidFlowData();
        $flowData['steps'][0]['component']['buttons'] = [
            ['text' => 'Button 1', 'type' => 'reply'],
            ['text' => 'Button 2', 'type' => 'reply'],
            ['text' => 'Button 3', 'type' => 'reply'],
            ['text' => 'Button 4', 'type' => 'reply'], // 4 buttons > 3 limit
        ];

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertFalse($result['valid']);
        $this->assertTrue(
            collect($result['errors'])->contains(function ($error) {
                return str_contains($error, 'tem mais de 3 botões');
            })
        );
    }

    public function test_button_text_too_long_fails_validation()
    {
        $flowData = $this->getValidFlowData();
        $flowData['steps'][0]['component']['buttons'][0]['text'] = str_repeat('a', 21); // 21 chars > 20 limit

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertFalse($result['valid']);
        $this->assertTrue(
            collect($result['errors'])->contains(function ($error) {
                return str_contains($error, 'texto muito longo para WhatsApp');
            })
        );
    }

    public function test_component_text_too_long_generates_warning()
    {
        $flowData = $this->getValidFlowData();
        $flowData['steps'][0]['component']['text'] = str_repeat('a', 4097); // > 4096 chars

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertTrue($result['valid']); // Should still be valid
        $this->assertTrue(
            collect($result['warnings'])->contains(function ($warning) {
                return str_contains($warning, 'texto muito longo');
            })
        );
    }

    public function test_step_without_component_generates_warning()
    {
        $flowData = $this->getValidFlowData();
        unset($flowData['steps'][0]['component']);

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertTrue($result['valid']); // Should still be valid
        $this->assertTrue(
            collect($result['warnings'])->contains(function ($warning) {
                return str_contains($warning, 'não tem componente definido');
            })
        );
    }

    public function test_parameter_without_type_generates_warning()
    {
        $flowData = $this->getValidFlowData();
        $flowData['steps'][0]['component']['parameters'] = [
            ['text' => 'Parameter without type']
        ];

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertTrue($result['valid']); // Should still be valid
        $this->assertTrue(
            collect($result['warnings'])->contains(function ($warning) {
                return str_contains($warning, 'tipo não definido');
            })
        );
    }

    public function test_infinite_loop_detection()
    {
        $flowData = $this->getValidFlowData();
        // Create a loop: step 0 -> step 1 -> step 0
        $flowData['steps'][0]['next_step'] = 1;
        $flowData['steps'][1]['next_step'] = 0;
        $flowData['steps'][1]['is_ending_step'] = false;

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertFalse($result['valid']);
        $this->assertTrue(
            collect($result['errors'])->contains(function ($error) {
                return str_contains($error, 'Loop infinito detectado');
            })
        );
    }

    public function test_orphaned_step_detection()
    {
        $flowData = $this->getValidFlowData();
        // Add a third step that's not referenced by any other step
        $flowData['steps'][] = [
            'step' => 'orphan',
            'step_type' => StepType::MESSAGE->value,
            'position' => 2,
            'is_initial_step' => false,
            'is_ending_step' => false,
            'component' => ['text' => 'Orphaned step']
        ];
        // Remove reference to step 1 from step 0
        unset($flowData['steps'][0]['next_step']);

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertTrue($result['valid']); // Should still be valid
        $this->assertTrue(
            collect($result['warnings'])->contains(function ($warning) {
                return str_contains($warning, 'pode estar órfão');
            })
        );
    }

    public function test_unreachable_step_detection()
    {
        $flowData = $this->getValidFlowData();
        // Add a third step that's not reachable from initial step
        $flowData['steps'][] = [
            'step' => 'unreachable',
            'step_type' => StepType::MESSAGE->value,
            'position' => 2,
            'is_initial_step' => false,
            'is_ending_step' => false,
            'component' => ['text' => 'Unreachable step']
        ];

        $result = $this->service->validateFlowStructure($flowData);

        $this->assertTrue($result['valid']); // Should still be valid
        $this->assertTrue(
            collect($result['warnings'])->contains(function ($warning) {
                return str_contains($warning, 'pode ser inalcançável');
            })
        );
    }
}
