<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorFactory;
use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface;
use App\Services\Meta\WhatsApp\ChatBot\Processors\MessageStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Processors\InteractiveStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Processors\InputStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Processors\CommandStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Processors\ConditionStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Processors\WebhookStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Processors\DelayStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Services\DynamicInputService;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\ExecuteCommand;
use App\Domains\ChatBot\Step;
use App\Enums\StepType;
use PHPUnit\Framework\TestCase;

class StepProcessorFactoryTest extends TestCase
{
    protected StepProcessorFactory $factory;
    protected DynamicInputService $dynamicInputService;
    protected ExecuteCommand $executeCommand;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dynamicInputService = $this->createMock(DynamicInputService::class);
        $this->executeCommand = $this->createMock(ExecuteCommand::class);
        $this->factory = new StepProcessorFactory($this->dynamicInputService, $this->executeCommand);
    }

    public function test_get_processor_for_message_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::MESSAGE;

        $processor = $this->factory->getProcessor($step);

        $this->assertInstanceOf(MessageStepProcessor::class, $processor);
        $this->assertInstanceOf(StepProcessorInterface::class, $processor);
    }

    public function test_get_processor_for_interactive_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::INTERACTIVE;

        $processor = $this->factory->getProcessor($step);

        $this->assertInstanceOf(InteractiveStepProcessor::class, $processor);
    }

    public function test_get_processor_for_input_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::INPUT;

        $processor = $this->factory->getProcessor($step);

        $this->assertInstanceOf(InputStepProcessor::class, $processor);
    }

    public function test_get_processor_for_command_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::COMMAND;

        $processor = $this->factory->getProcessor($step);

        $this->assertInstanceOf(CommandStepProcessor::class, $processor);
    }

    public function test_get_processor_for_condition_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::CONDITION;

        $processor = $this->factory->getProcessor($step);

        $this->assertInstanceOf(ConditionStepProcessor::class, $processor);
    }

    public function test_get_processor_for_webhook_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::WEBHOOK;

        $processor = $this->factory->getProcessor($step);

        $this->assertInstanceOf(WebhookStepProcessor::class, $processor);
    }

    public function test_get_processor_for_delay_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::DELAY;

        $processor = $this->factory->getProcessor($step);

        $this->assertInstanceOf(DelayStepProcessor::class, $processor);
    }

    public function test_get_processor_throws_exception_for_null_step_type()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = null;
        $step->id = 123;

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Step 123 has no step_type defined');

        $this->factory->getProcessor($step);
    }

    public function test_get_processor_by_type()
    {
        $processor = $this->factory->getProcessorByType(StepType::MESSAGE->value);
        $this->assertInstanceOf(MessageStepProcessor::class, $processor);

        $processor = $this->factory->getProcessorByType(StepType::INTERACTIVE->value);
        $this->assertInstanceOf(InteractiveStepProcessor::class, $processor);

        $processor = $this->factory->getProcessorByType(StepType::INPUT->value);
        $this->assertInstanceOf(InputStepProcessor::class, $processor);
    }

    public function test_get_processor_by_type_throws_exception_for_invalid_type()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('No processor found for step type: invalid_type');

        $this->factory->getProcessorByType('invalid_type');
    }

    public function test_get_all_processors()
    {
        $processors = $this->factory->getAllProcessors();

        $this->assertIsArray($processors);
        $this->assertCount(7, $processors); // All 7 step types

        $this->assertArrayHasKey(StepType::MESSAGE->value, $processors);
        $this->assertArrayHasKey(StepType::INTERACTIVE->value, $processors);
        $this->assertArrayHasKey(StepType::INPUT->value, $processors);
        $this->assertArrayHasKey(StepType::COMMAND->value, $processors);
        $this->assertArrayHasKey(StepType::CONDITION->value, $processors);
        $this->assertArrayHasKey(StepType::WEBHOOK->value, $processors);
        $this->assertArrayHasKey(StepType::DELAY->value, $processors);

        foreach ($processors as $processor) {
            $this->assertInstanceOf(StepProcessorInterface::class, $processor);
        }
    }

    public function test_get_supported_step_types()
    {
        $supportedTypes = $this->factory->getSupportedStepTypes();

        $this->assertIsArray($supportedTypes);
        $this->assertCount(7, $supportedTypes);

        $this->assertContains(StepType::MESSAGE->value, $supportedTypes);
        $this->assertContains(StepType::INTERACTIVE->value, $supportedTypes);
        $this->assertContains(StepType::INPUT->value, $supportedTypes);
        $this->assertContains(StepType::COMMAND->value, $supportedTypes);
        $this->assertContains(StepType::CONDITION->value, $supportedTypes);
        $this->assertContains(StepType::WEBHOOK->value, $supportedTypes);
        $this->assertContains(StepType::DELAY->value, $supportedTypes);
    }

    public function test_is_step_type_supported()
    {
        $this->assertTrue($this->factory->isStepTypeSupported(StepType::MESSAGE->value));
        $this->assertTrue($this->factory->isStepTypeSupported(StepType::INTERACTIVE->value));
        $this->assertTrue($this->factory->isStepTypeSupported(StepType::INPUT->value));
        $this->assertTrue($this->factory->isStepTypeSupported(StepType::COMMAND->value));
        $this->assertTrue($this->factory->isStepTypeSupported(StepType::CONDITION->value));
        $this->assertTrue($this->factory->isStepTypeSupported(StepType::WEBHOOK->value));
        $this->assertTrue($this->factory->isStepTypeSupported(StepType::DELAY->value));

        $this->assertFalse($this->factory->isStepTypeSupported('invalid_type'));
    }

    public function test_register_custom_processor()
    {
        $customProcessor = $this->createMock(StepProcessorInterface::class);

        $this->factory->registerProcessor('custom_type', $customProcessor);

        $this->assertTrue($this->factory->isStepTypeSupported('custom_type'));
        $this->assertSame($customProcessor, $this->factory->getProcessorByType('custom_type'));
    }

    public function test_unregister_processor()
    {
        // First verify the processor exists
        $this->assertTrue($this->factory->isStepTypeSupported(StepType::MESSAGE->value));

        // Unregister it
        $this->factory->unregisterProcessor(StepType::MESSAGE->value);

        // Verify it's no longer supported
        $this->assertFalse($this->factory->isStepTypeSupported(StepType::MESSAGE->value));

        // Verify exception is thrown when trying to get it
        $this->expectException(\InvalidArgumentException::class);
        $this->factory->getProcessorByType(StepType::MESSAGE->value);
    }

    public function test_app_make_works()
    {
        // Test that we can use app()->make() to instantiate the factory
        $dynamicInputService = $this->createMock(DynamicInputService::class);
        $executeCommand = $this->createMock(ExecuteCommand::class);
        $factory = new StepProcessorFactory($dynamicInputService, $executeCommand);

        $this->assertInstanceOf(StepProcessorFactory::class, $factory);
    }
}
