<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Services\Meta\WhatsApp\ChatBot\Processors\InputStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface;
use App\Services\Meta\WhatsApp\ChatBot\Services\DynamicInputService;
use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;
use PHPUnit\Framework\TestCase;

class InputStepProcessorTest extends TestCase
{
    protected InputStepProcessor $processor;
    protected DynamicInputService $dynamicInputService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dynamicInputService = $this->createMock(DynamicInputService::class);
        $this->processor = new InputStepProcessor($this->dynamicInputService);
    }

    public function test_implements_step_processor_interface()
    {
        $this->assertInstanceOf(StepProcessorInterface::class, $this->processor);
    }

    public function test_get_supported_step_types()
    {
        $supportedTypes = $this->processor->getSupportedStepTypes();
        
        $this->assertIsArray($supportedTypes);
        $this->assertCount(1, $supportedTypes);
        $this->assertContains(StepType::INPUT->value, $supportedTypes);
    }

    public function test_can_process_input_step()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::INPUT;
        
        $this->assertTrue($this->processor->canProcess($step));
    }

    public function test_cannot_process_non_input_step()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::MESSAGE;
        
        $this->assertFalse($this->processor->canProcess($step));
    }

    public function test_process_input_step_requests_input_when_no_text()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->id = 123;
        $step->step = 'Please enter your name';
        $step->json = null;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('getTextContent')->willReturn(null);
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should request input
        $this->assertEquals('input', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('request_input', $result['action']);
        $this->assertEquals('Please enter your name', $result['message']);
        $this->assertFalse($result['move_to_next']);
    }

    public function test_process_input_step_with_successful_input_processing()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->id = 123;
        $step->next_step = 456;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('getTextContent')->willReturn('John Doe');
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        // Mock successful input processing
        $this->dynamicInputService
            ->method('processInputStep')
            ->willReturn([
                'success' => true,
                'updated_field' => 'name',
                'updated_value' => 'John Doe'
            ]);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should process input successfully
        $this->assertEquals('input', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('input_processed', $result['action']);
        $this->assertEquals('John Doe', $result['input']);
        $this->assertEquals('name', $result['updated_field']);
        $this->assertEquals('John Doe', $result['updated_value']);
        $this->assertStringContainsString('name', $result['message']);
        $this->assertEquals(456, $result['next_step']);
        $this->assertTrue($result['move_to_next']);
    }

    public function test_process_input_step_with_failed_input_processing()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->id = 123;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('getTextContent')->willReturn('invalid input');
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        // Mock failed input processing
        $this->dynamicInputService
            ->method('processInputStep')
            ->willReturn([
                'success' => false,
                'error' => 'Invalid email format'
            ]);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should handle input error
        $this->assertEquals('input', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('input_invalid', $result['action']);
        $this->assertEquals('Invalid email format', $result['error']);
        $this->assertStringContainsString('Invalid email format', $result['message']);
        $this->assertFalse($result['move_to_next']);
    }

    public function test_process_input_step_with_finish_flow_error()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->id = 123;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('getTextContent')->willReturn('critical error input');
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        // Mock critical error that should finish flow
        $this->dynamicInputService
            ->method('processInputStep')
            ->willReturn([
                'success' => false,
                'error' => 'Critical system error',
                'finish_flow' => true
            ]);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should finish conversation
        $this->assertEquals('input', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('input_error_finish', $result['action']);
        $this->assertEquals('Critical system error', $result['error']);
        $this->assertStringContainsString('Critical system error', $result['message']);
        $this->assertFalse($result['move_to_next']);
        $this->assertTrue($result['finish_conversation']);
    }

    public function test_get_step_message_from_json_prompt()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'Fallback message';
        $step->json = json_encode(['prompt' => 'Enter your email address']);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('getTextContent')->willReturn(null);
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertEquals('Enter your email address', $result['message']);
    }

    public function test_get_step_message_from_json_message()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'Fallback message';
        $step->json = json_encode(['message' => 'Please provide input']);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('getTextContent')->willReturn(null);
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertEquals('Please provide input', $result['message']);
    }

    public function test_get_step_message_from_json_text()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'Fallback message';
        $step->json = json_encode(['text' => 'Input text field']);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('getTextContent')->willReturn(null);
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertEquals('Input text field', $result['message']);
    }

    public function test_get_step_message_fallback()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = null;
        $step->json = null;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('getTextContent')->willReturn(null);
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertEquals('Please provide your input:', $result['message']);
    }

    public function test_app_make_works()
    {
        // Test that we can use app()->make() to instantiate the processor
        // Note: This would require proper service container binding in real app
        $dynamicInputService = $this->createMock(DynamicInputService::class);
        $processor = new InputStepProcessor($dynamicInputService);
        
        $this->assertInstanceOf(InputStepProcessor::class, $processor);
        $this->assertInstanceOf(StepProcessorInterface::class, $processor);
    }
}
