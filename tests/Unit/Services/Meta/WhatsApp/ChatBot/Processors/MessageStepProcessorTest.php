<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Services\Meta\WhatsApp\ChatBot\Processors\MessageStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface;
use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;
use PHPUnit\Framework\TestCase;

class MessageStepProcessorTest extends TestCase
{
    protected MessageStepProcessor $processor;

    protected function setUp(): void
    {
        parent::setUp();
        $this->processor = new MessageStepProcessor();
    }

    public function test_implements_step_processor_interface()
    {
        $this->assertInstanceOf(StepProcessorInterface::class, $this->processor);
    }

    public function test_get_supported_step_types()
    {
        $supportedTypes = $this->processor->getSupportedStepTypes();
        
        $this->assertIsArray($supportedTypes);
        $this->assertCount(1, $supportedTypes);
        $this->assertContains(StepType::MESSAGE->value, $supportedTypes);
    }

    public function test_can_process_message_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::MESSAGE;
        
        $this->assertTrue($this->processor->canProcess($step));
    }

    public function test_cannot_process_non_message_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::INTERACTIVE;
        
        $this->assertFalse($this->processor->canProcess($step));
    }

    public function test_process_message_step_with_basic_configuration()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'Welcome message';
        $step->next_step = 456;
        $step->json = null;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Verify result structure
        $this->assertIsArray($result);
        $this->assertEquals('message', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('send_message', $result['action']);
        $this->assertEquals('Welcome message', $result['message']);
        $this->assertEquals(456, $result['next_step']);
        $this->assertTrue($result['move_to_next']);
    }

    public function test_process_message_step_with_json_message()
    {
        // Create mock step with JSON configuration
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'Fallback message';
        $step->next_step = 456;
        $step->json = json_encode(['message' => 'JSON configured message']);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should use JSON message over step name
        $this->assertEquals('JSON configured message', $result['message']);
    }

    public function test_process_message_step_with_json_text_field()
    {
        // Create mock step with JSON configuration using 'text' field
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'Fallback message';
        $step->next_step = 456;
        $step->json = json_encode(['text' => 'JSON text field message']);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should use JSON text field
        $this->assertEquals('JSON text field message', $result['message']);
    }

    public function test_process_message_step_with_invalid_json()
    {
        // Create mock step with invalid JSON
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'Fallback message';
        $step->next_step = 456;
        $step->json = 'invalid json';
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should fallback to step name
        $this->assertEquals('Fallback message', $result['message']);
    }

    public function test_process_message_step_with_empty_step_name()
    {
        // Create mock step with empty step name
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = null;
        $step->next_step = 456;
        $step->json = null;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should use default message
        $this->assertEquals('Step message not configured', $result['message']);
    }

    public function test_process_message_step_with_null_next_step()
    {
        // Create mock step with null next_step
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'Final message';
        $step->next_step = null;
        $step->json = null;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should handle null next_step
        $this->assertNull($result['next_step']);
        $this->assertTrue($result['move_to_next']);
    }

    public function test_app_make_works()
    {
        // Test that we can use app()->make() to instantiate the processor
        $processor = $this->createMock(MessageStepProcessor::class);
        
        $this->assertInstanceOf(MessageStepProcessor::class, $processor);
        $this->assertInstanceOf(StepProcessorInterface::class, $processor);
    }
}
