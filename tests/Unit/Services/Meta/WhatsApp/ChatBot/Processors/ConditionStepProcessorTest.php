<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Services\Meta\WhatsApp\ChatBot\Processors\ConditionStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface;
use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;
use PHPUnit\Framework\TestCase;

class ConditionStepProcessorTest extends TestCase
{
    protected ConditionStepProcessor $processor;

    protected function setUp(): void
    {
        parent::setUp();
        $this->processor = new ConditionStepProcessor();
    }

    public function test_implements_step_processor_interface()
    {
        $this->assertInstanceOf(StepProcessorInterface::class, $this->processor);
    }

    public function test_get_supported_step_types()
    {
        $supportedTypes = $this->processor->getSupportedStepTypes();
        
        $this->assertIsArray($supportedTypes);
        $this->assertCount(1, $supportedTypes);
        $this->assertContains(StepType::CONDITION->value, $supportedTypes);
    }

    public function test_can_process_condition_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::CONDITION;
        
        $this->assertTrue($this->processor->canProcess($step));
    }

    public function test_cannot_process_non_condition_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::MESSAGE;
        
        $this->assertFalse($this->processor->canProcess($step));
    }

    public function test_process_condition_step_without_configuration()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = null;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should use default next step when no condition config
        $this->assertEquals('condition', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('no_condition_config', $result['action']);
        $this->assertEquals(456, $result['next_step']);
        $this->assertTrue($result['move_to_next']);
        $this->assertNull($result['condition_result']);
    }

    public function test_process_condition_step_with_invalid_json()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = 'invalid json';
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should use default next step when JSON is invalid
        $this->assertEquals('no_condition_config', $result['action']);
        $this->assertEquals(456, $result['next_step']);
    }

    public function test_process_condition_step_with_json_without_conditions()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = json_encode(['other_field' => 'value']);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should use default next step when no conditions field
        $this->assertEquals('no_condition_config', $result['action']);
        $this->assertEquals(456, $result['next_step']);
    }

    public function test_process_condition_step_with_valid_conditions()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = json_encode([
            'conditions' => [
                ['field' => 'client.name', 'operator' => 'exists'],
                ['field' => 'client.email', 'operator' => 'not_empty']
            ],
            'default_path' => 789
        ]);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should evaluate conditions
        $this->assertEquals('condition', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('evaluate_condition', $result['action']);
        $this->assertIsArray($result['condition_result']);
        $this->assertCount(2, $result['condition_result']);
        $this->assertTrue($result['move_to_next']);
        
        // Should have conditions evaluated array
        $this->assertArrayHasKey('conditions_evaluated', $result);
        $this->assertCount(2, $result['conditions_evaluated']);
    }

    public function test_process_condition_step_uses_default_path()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = json_encode([
            'conditions' => [
                ['field' => 'client.name', 'operator' => 'exists']
            ],
            'default_path' => 789
        ]);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should use default_path from configuration
        $this->assertEquals(789, $result['next_step']);
    }

    public function test_process_condition_step_falls_back_to_step_next_step()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = json_encode([
            'conditions' => [
                ['field' => 'client.name', 'operator' => 'exists']
            ]
            // No default_path specified
        ]);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should fall back to step's next_step
        $this->assertEquals(456, $result['next_step']);
    }

    public function test_evaluate_single_condition_returns_true_placeholder()
    {
        // Since the actual condition evaluation is not implemented yet,
        // test that it returns the placeholder true value
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = json_encode([
            'conditions' => [
                ['field' => 'client.name', 'operator' => 'exists']
            ]
        ]);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // All conditions should return true (placeholder implementation)
        $this->assertCount(1, $result['condition_result']);
        $this->assertTrue($result['condition_result'][0]['result']);
        $this->assertEquals(['field' => 'client.name', 'operator' => 'exists'], $result['condition_result'][0]['condition']);
    }

    public function test_process_condition_step_with_multiple_conditions()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = json_encode([
            'conditions' => [
                ['field' => 'client.name', 'operator' => 'exists'],
                ['field' => 'client.email', 'operator' => 'not_empty'],
                ['field' => 'client.phone', 'operator' => 'valid_format']
            ]
        ]);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should evaluate all conditions
        $this->assertCount(3, $result['condition_result']);
        $this->assertCount(3, $result['conditions_evaluated']);
        
        // All should return true (placeholder implementation)
        foreach ($result['condition_result'] as $conditionResult) {
            $this->assertTrue($conditionResult['result']);
            $this->assertArrayHasKey('condition', $conditionResult);
        }
    }

    public function test_app_make_works()
    {
        $processor = $this->createMock(ConditionStepProcessor::class);
        
        $this->assertInstanceOf(ConditionStepProcessor::class, $processor);
        $this->assertInstanceOf(StepProcessorInterface::class, $processor);
    }
}
