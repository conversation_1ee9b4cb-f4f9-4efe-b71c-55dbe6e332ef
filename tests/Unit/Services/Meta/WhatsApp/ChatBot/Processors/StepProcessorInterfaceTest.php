<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface;
use App\Services\Meta\WhatsApp\ChatBot\Processors\MessageStepProcessor;
use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;
use PHPUnit\Framework\TestCase;

class StepProcessorInterfaceTest extends TestCase
{
    public function test_interface_contract()
    {
        // Test that the interface exists and has the expected methods
        $this->assertTrue(interface_exists(StepProcessorInterface::class));
        
        $reflection = new \ReflectionClass(StepProcessorInterface::class);
        
        // Check that required methods exist
        $this->assertTrue($reflection->hasMethod('process'));
        $this->assertTrue($reflection->hasMethod('canProcess'));
        $this->assertTrue($reflection->hasMethod('getSupportedStepTypes'));
        
        // Check method signatures
        $processMethod = $reflection->getMethod('process');
        $this->assertEquals(3, $processMethod->getNumberOfRequiredParameters());
        
        $canProcessMethod = $reflection->getMethod('canProcess');
        $this->assertEquals(1, $canProcessMethod->getNumberOfRequiredParameters());
        
        $getSupportedStepTypesMethod = $reflection->getMethod('getSupportedStepTypes');
        $this->assertEquals(0, $getSupportedStepTypesMethod->getNumberOfRequiredParameters());
    }

    public function test_concrete_implementation_follows_interface()
    {
        // Test that a concrete implementation properly implements the interface
        $processor = new MessageStepProcessor();
        
        $this->assertInstanceOf(StepProcessorInterface::class, $processor);
        
        // Test that all interface methods are callable
        $this->assertTrue(method_exists($processor, 'process'));
        $this->assertTrue(method_exists($processor, 'canProcess'));
        $this->assertTrue(method_exists($processor, 'getSupportedStepTypes'));
    }

    public function test_supported_step_types_returns_array()
    {
        $processor = new MessageStepProcessor();
        
        $supportedTypes = $processor->getSupportedStepTypes();
        
        $this->assertIsArray($supportedTypes);
        $this->assertNotEmpty($supportedTypes);
        $this->assertContains(StepType::MESSAGE->value, $supportedTypes);
    }

    public function test_can_process_with_valid_step()
    {
        $processor = new MessageStepProcessor();
        
        // Create a mock step with MESSAGE type
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::MESSAGE;
        
        $this->assertTrue($processor->canProcess($step));
    }

    public function test_can_process_with_invalid_step()
    {
        $processor = new MessageStepProcessor();
        
        // Create a mock step with different type
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::INTERACTIVE;
        
        $this->assertFalse($processor->canProcess($step));
    }

    public function test_process_returns_array()
    {
        $processor = new MessageStepProcessor();
        
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 1;
        $step->step = 'Test message';
        $step->next_step = 2;
        $step->json = null;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $processor->process($step, $interaction, $conversation);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('type', $result);
        $this->assertArrayHasKey('step_id', $result);
        $this->assertArrayHasKey('action', $result);
        $this->assertArrayHasKey('move_to_next', $result);
        
        $this->assertEquals('message', $result['type']);
        $this->assertEquals(1, $result['step_id']);
        $this->assertTrue($result['move_to_next']);
    }

    public function test_app_make_works()
    {
        // Test that we can instantiate processors using app()->make()
        $processor = $this->createMock(MessageStepProcessor::class);
        
        $this->assertInstanceOf(StepProcessorInterface::class, $processor);
        $this->assertInstanceOf(MessageStepProcessor::class, $processor);
    }
}
