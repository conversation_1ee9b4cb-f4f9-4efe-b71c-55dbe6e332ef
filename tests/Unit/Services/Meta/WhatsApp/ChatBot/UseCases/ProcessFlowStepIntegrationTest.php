<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\UseCases;

use Tests\TestCase;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessFlowStep;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppInteractionRepository;
use App\Services\Meta\WhatsApp\ChatBot\Factories\WhatsAppInteractionFactory;
use App\Services\Meta\WhatsApp\ChatBot\Services\ConditionalNavigationService;
use App\Services\Meta\WhatsApp\ChatBot\Services\ErrorHandlerService;
use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorFactory;
use App\Services\Meta\WhatsApp\ChatBot\Services\DynamicInputService;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\ExecuteCommand;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Domains\ChatBot\Step;
use App\Enums\StepType;

class ProcessFlowStepIntegrationTest extends TestCase
{
    protected ProcessFlowStep $processFlowStep;
    protected $mockInteractionRepository;
    protected $mockInteractionFactory;
    protected $mockConditionalNavigationService;
    protected $mockErrorHandlerService;
    protected StepProcessorFactory $stepProcessorFactory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mocks for dependencies
        $this->mockInteractionRepository = $this->createMock(WhatsAppInteractionRepository::class);
        $this->mockInteractionFactory = $this->createMock(WhatsAppInteractionFactory::class);
        $this->mockConditionalNavigationService = $this->createMock(ConditionalNavigationService::class);
        $this->mockErrorHandlerService = $this->createMock(ErrorHandlerService::class);

        // Create real StepProcessorFactory with mocked dependencies
        $mockDynamicInputService = $this->createMock(DynamicInputService::class);
        $mockExecuteCommand = $this->createMock(ExecuteCommand::class);
        $this->stepProcessorFactory = new StepProcessorFactory($mockDynamicInputService, $mockExecuteCommand);

        $this->processFlowStep = new ProcessFlowStep(
            $this->mockInteractionRepository,
            $this->mockInteractionFactory,
            $this->mockConditionalNavigationService,
            $this->mockErrorHandlerService,
            $this->stepProcessorFactory
        );
    }

    public function test_strategy_pattern_integration_with_message_step()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'message_id' => 'wamid.123',
            'type' => 'text',
            'text' => ['body' => 'Hello']
        ];

        $step = $this->createMock(Step::class);
        $step->id = 1;
        $step->step_type = StepType::MESSAGE;
        $step->name = 'Welcome Message';
        $step->next_step = 2;
        $step->json = '{"message": "Welcome to our service!"}';
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->current_step = $step;

        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->id = 1;

        $savedInteraction = $this->createMock(WhatsAppInteraction::class);
        $savedInteraction->id = 1;

        // Set up mocks
        $this->mockInteractionFactory
            ->expects($this->once())
            ->method('buildFromWebhookData')
            ->with($messageData, $conversation)
            ->willReturn($interaction);

        $this->mockInteractionRepository
            ->expects($this->exactly(2))
            ->method('save')
            ->willReturnOnConsecutiveCalls($savedInteraction, $savedInteraction);

        $this->mockConditionalNavigationService
            ->expects($this->once())
            ->method('hasConditionalNavigation')
            ->willReturn(false);

        // Act
        $result = $this->processFlowStep->perform($conversation, $messageData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('message', $result['type']);
        $this->assertEquals(1, $result['step_id']);
        $this->assertEquals('send_message', $result['action']);
        $this->assertTrue($result['move_to_next']);
        $this->assertEquals(2, $result['next_step']);
    }

    public function test_strategy_pattern_can_handle_all_step_types()
    {
        // Test that the factory can create processors for all step types
        $stepTypes = [
            StepType::MESSAGE,
            StepType::INTERACTIVE,
            StepType::INPUT,
            StepType::COMMAND,
            StepType::CONDITION,
            StepType::WEBHOOK,
            StepType::DELAY
        ];

        foreach ($stepTypes as $index => $stepType) {
            // Create a fresh mock for each iteration
            $step = $this->getMockBuilder(Step::class)
                ->disableOriginalConstructor()
                ->getMock();
            $step->step_type = $stepType;
            $step->json = '{}';
            $step->expects($this->any())->method('setStepTypeFromLegacyFields');

            $processor = $this->stepProcessorFactory->getProcessor($step);

            $this->assertInstanceOf(
                \App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface::class,
                $processor,
                "Failed to create processor for step type: {$stepType->value}"
            );

            // Create another fresh mock for canProcess test to avoid conflicts
            $stepForCanProcess = $this->getMockBuilder(Step::class)
                ->disableOriginalConstructor()
                ->getMock();
            $stepForCanProcess->step_type = $stepType;
            $stepForCanProcess->json = '{}';
            $stepForCanProcess->expects($this->any())->method('setStepTypeFromLegacyFields');

            $this->assertTrue(
                $processor->canProcess($stepForCanProcess),
                "Processor cannot process step type: {$stepType->value}"
            );

            $this->assertContains(
                $stepType->value,
                $processor->getSupportedStepTypes(),
                "Processor does not support step type: {$stepType->value}"
            );
        }
    }

    public function test_step_processor_factory_integration()
    {
        // Test that the factory is properly integrated
        $this->assertInstanceOf(StepProcessorFactory::class, $this->stepProcessorFactory);

        // Test that all processors are available
        $allProcessors = $this->stepProcessorFactory->getAllProcessors();
        $this->assertCount(7, $allProcessors, 'Should have 7 processors');

        // Test that all step types are supported
        $supportedTypes = $this->stepProcessorFactory->getSupportedStepTypes();
        $this->assertCount(7, $supportedTypes, 'Should support 7 step types');

        foreach ([StepType::MESSAGE, StepType::INTERACTIVE, StepType::INPUT, StepType::COMMAND, StepType::CONDITION, StepType::WEBHOOK, StepType::DELAY] as $stepType) {
            $this->assertTrue(
                $this->stepProcessorFactory->isStepTypeSupported($stepType->value),
                "Step type {$stepType->value} should be supported"
            );
        }
    }
}
