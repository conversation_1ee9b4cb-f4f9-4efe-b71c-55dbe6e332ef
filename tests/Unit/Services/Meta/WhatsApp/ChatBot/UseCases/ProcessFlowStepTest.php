<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\UseCases;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessFlowStep;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppInteractionRepository;
use App\Services\Meta\WhatsApp\ChatBot\Factories\WhatsAppInteractionFactory;
use App\Services\Meta\WhatsApp\ChatBot\Services\ConditionalNavigationService;
use App\Services\Meta\WhatsApp\ChatBot\Services\DynamicInputService;
use App\Services\Meta\WhatsApp\ChatBot\Services\ErrorHandlerService;
use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorFactory;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Domains\ChatBot\Step;
use Exception;

class ProcessFlowStepTest extends TestCase
{
    use RefreshDatabase;

    protected ProcessFlowStep $processFlowStep;
    protected $mockInteractionRepository;
    protected $mockInteractionFactory;
    protected $mockConditionalNavigationService;
    protected $mockErrorHandlerService;
    protected $mockStepProcessorFactory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mocks
        $this->mockInteractionRepository = $this->createMock(WhatsAppInteractionRepository::class);
        $this->mockInteractionFactory = $this->createMock(WhatsAppInteractionFactory::class);
        $this->mockConditionalNavigationService = $this->createMock(ConditionalNavigationService::class);
        $this->mockErrorHandlerService = $this->createMock(ErrorHandlerService::class);
        $this->mockStepProcessorFactory = $this->createMock(StepProcessorFactory::class);

        $this->processFlowStep = new ProcessFlowStep(
            $this->mockInteractionRepository,
            $this->mockInteractionFactory,
            $this->mockConditionalNavigationService,
            $this->mockErrorHandlerService,
            $this->mockStepProcessorFactory
        );
    }

    public function test_perform_processes_message_step()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'message_id' => 'wamid.123',
            'type' => 'text',
            'text' => ['body' => 'Hello']
        ];

        $step = $this->createMock(Step::class);
        $step->id = 1;
        $step->type = 'message';
        $step->message = 'Welcome to our service!';
        $step->next_step = 2;

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->current_step = $step;

        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->id = 1;

        $savedInteraction = $this->createMock(WhatsAppInteraction::class);
        $savedInteraction->id = 1;

        // Set up mocks
        $this->mockInteractionFactory
            ->expects($this->once())
            ->method('buildFromWebhookData')
            ->with($messageData, $conversation)
            ->willReturn($interaction);

        $this->mockInteractionRepository
            ->expects($this->exactly(2))
            ->method('save')
            ->willReturnOnConsecutiveCalls($savedInteraction, $savedInteraction);

        // Mock step processor factory to return a mock processor
        $mockProcessor = $this->createMock(\App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface::class);
        $mockProcessor->method('process')->willReturn([
            'type' => 'message',
            'step_id' => 1,
            'action' => 'send_message',
            'message' => 'Welcome to our service!',
            'move_to_next' => true,
            'next_step' => 2
        ]);

        $this->mockStepProcessorFactory
            ->expects($this->once())
            ->method('getProcessor')
            ->willReturn($mockProcessor);

        // Mock step processor factory to return a mock processor
        $mockProcessor = $this->createMock(\App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface::class);
        $mockProcessor->method('process')->willReturn([
            'type' => 'message',
            'step_id' => 1,
            'action' => 'send_message',
            'message' => 'Test message',
            'move_to_next' => true,
            'next_step' => 2
        ]);

        $this->mockStepProcessorFactory
            ->expects($this->once())
            ->method('getProcessor')
            ->willReturn($mockProcessor);

        // Act
        $result = $this->processFlowStep->perform($conversation, $messageData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('message', $result['type']);
        $this->assertEquals(1, $result['step_id']);
        $this->assertEquals('send_message', $result['action']);
        $this->assertEquals('Welcome to our service!', $result['message']);
        $this->assertEquals(2, $result['next_step']);
        $this->assertTrue($result['move_to_next']);
    }

    public function test_perform_processes_interactive_step_show_options()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'message_id' => 'wamid.456',
            'type' => 'text',
            'text' => ['body' => 'Start']
        ];

        $step = $this->createMock(Step::class);
        $step->id = 2;
        $step->type = 'interactive';
        $step->message = 'Choose an option:';

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->current_step = $step;

        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->id = 2;

        $savedInteraction = $this->createMock(WhatsAppInteraction::class);

        // Set up mocks
        $this->mockInteractionFactory
            ->expects($this->once())
            ->method('buildFromWebhookData')
            ->willReturn($interaction);

        $this->mockInteractionRepository
            ->expects($this->exactly(2))
            ->method('save')
            ->willReturn($savedInteraction);

        $this->mockConditionalNavigationService
            ->expects($this->never())
            ->method('hasConditionalNavigation');

        // Mock step processor factory to return a mock processor
        $mockProcessor = $this->createMock(\App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface::class);
        $mockProcessor->method('process')->willReturn([
            'type' => 'interactive',
            'step_id' => 2,
            'action' => 'show_options',
            'message' => 'Choose an option:',
            'options' => ['Option 1', 'Option 2'],
            'move_to_next' => false
        ]);

        $this->mockStepProcessorFactory
            ->expects($this->once())
            ->method('getProcessor')
            ->willReturn($mockProcessor);

        // Act
        $result = $this->processFlowStep->perform($conversation, $messageData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('interactive', $result['type']);
        $this->assertEquals(2, $result['step_id']);
        $this->assertEquals('show_options', $result['action']);
        $this->assertEquals('Choose an option:', $result['message']);
        $this->assertArrayHasKey('options', $result);
        $this->assertFalse($result['move_to_next']);
    }

    public function test_perform_processes_interactive_step_with_selection()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'message_id' => 'wamid.789',
            'type' => 'interactive',
            'interactive' => [
                'type' => 'button_reply',
                'button_reply' => [
                    'id' => 'option_1',
                    'title' => 'Yes'
                ]
            ]
        ];

        $step = $this->createMock(Step::class);
        $step->id = 3;
        $step->type = 'interactive';

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->current_step = $step;

        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->id = 3;

        $savedInteraction = $this->createMock(WhatsAppInteraction::class);

        // Set up mocks
        $this->mockInteractionFactory
            ->expects($this->once())
            ->method('buildFromWebhookData')
            ->willReturn($interaction);

        $this->mockInteractionRepository
            ->expects($this->exactly(2))
            ->method('save')
            ->willReturn($savedInteraction);

        // Mock step processor factory
        $mockProcessor = $this->createMock(\App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface::class);
        $mockProcessor->method('process')->willReturn([
            'type' => 'test',
            'step_id' => 1,
            'action' => 'test_action',
            'message' => 'Test message',
            'move_to_next' => true,
            'next_step' => 2
        ]);
        
        $this->mockStepProcessorFactory
            ->method('getProcessor')
            ->willReturn($mockProcessor);
        
        // Act
        $result = $this->processFlowStep->perform($conversation, $messageData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('interactive', $result['type']);
        $this->assertEquals(3, $result['step_id']);
        $this->assertEquals('process_selection', $result['action']);
        $this->assertArrayHasKey('selection', $result);
        $this->assertArrayHasKey('next_step', $result);
        $this->assertTrue($result['move_to_next']);
    }

    public function test_perform_processes_input_step()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'message_id' => 'wamid.input',
            'type' => 'text',
            'text' => ['body' => 'John Doe']
        ];

        $step = $this->createMock(Step::class);
        $step->id = 4;
        $step->type = 'input';
        $step->is_input = true;
        $step->input_field = 'client.name';

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->current_step = $step;

        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->id = 4;

        $savedInteraction = $this->createMock(WhatsAppInteraction::class);

        // Set up mocks
        $this->mockInteractionFactory
            ->expects($this->once())
            ->method('buildFromWebhookData')
            ->willReturn($interaction);

        $this->mockInteractionRepository
            ->expects($this->exactly(2))
            ->method('save')
            ->willReturn($savedInteraction);

        $this->mockDynamicInputService
            ->expects($this->once())
            ->method('processInput')
            ->with($step, $interaction, $conversation)
            ->willReturn(['success' => true, 'field' => 'client.name', 'value' => 'John Doe']);

        // Mock step processor factory
        $mockProcessor = $this->createMock(\App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface::class);
        $mockProcessor->method('process')->willReturn([
            'type' => 'test',
            'step_id' => 1,
            'action' => 'test_action',
            'message' => 'Test message',
            'move_to_next' => true,
            'next_step' => 2
        ]);
        
        $this->mockStepProcessorFactory
            ->method('getProcessor')
            ->willReturn($mockProcessor);
        
        // Act
        $result = $this->processFlowStep->perform($conversation, $messageData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('input', $result['type']);
        $this->assertEquals(4, $result['step_id']);
        $this->assertEquals('collect_input', $result['action']);
        $this->assertArrayHasKey('input_result', $result);
        $this->assertTrue($result['move_to_next']);
    }

    public function test_perform_throws_exception_when_no_current_step()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'message_id' => 'wamid.nostep'
        ];

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->current_step = null;

        $interaction = $this->createMock(WhatsAppInteraction::class);

        $this->mockInteractionFactory
            ->expects($this->once())
            ->method('buildFromWebhookData')
            ->willReturn($interaction);

        $this->mockInteractionRepository
            ->expects($this->once())
            ->method('save')
            ->willReturn($interaction);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('No current step found for conversation');

        $this->processFlowStep->perform($conversation, $messageData);
    }

    public function test_perform_handles_unknown_step_type()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'message_id' => 'wamid.unknown'
        ];

        $step = $this->createMock(Step::class);
        $step->id = 5;
        $step->type = 'unknown_type';

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->current_step = $step;

        $interaction = $this->createMock(WhatsAppInteraction::class);
        $savedInteraction = $this->createMock(WhatsAppInteraction::class);

        $this->mockInteractionFactory
            ->expects($this->once())
            ->method('buildFromWebhookData')
            ->willReturn($interaction);

        $this->mockInteractionRepository
            ->expects($this->exactly(2))
            ->method('save')
            ->willReturn($savedInteraction);

        // Mock step processor factory
        $mockProcessor = $this->createMock(\App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface::class);
        $mockProcessor->method('process')->willReturn([
            'type' => 'test',
            'step_id' => 1,
            'action' => 'test_action',
            'message' => 'Test message',
            'move_to_next' => true,
            'next_step' => 2
        ]);
        
        $this->mockStepProcessorFactory
            ->method('getProcessor')
            ->willReturn($mockProcessor);
        
        // Act
        $result = $this->processFlowStep->perform($conversation, $messageData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('unknown', $result['type']);
        $this->assertEquals(5, $result['step_id']);
        $this->assertEquals('unknown_step_type', $result['action']);
        $this->assertFalse($result['move_to_next']);
    }

    public function test_perform_handles_interaction_factory_exception()
    {
        // Arrange
        $messageData = ['invalid' => 'data'];
        $conversation = $this->createMock(WhatsAppConversation::class);

        $this->mockInteractionFactory
            ->expects($this->once())
            ->method('buildFromWebhookData')
            ->willThrowException(new Exception('Interaction creation failed'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Interaction creation failed');

        $this->processFlowStep->perform($conversation, $messageData);
    }

    public function test_perform_handles_interaction_save_exception()
    {
        // Arrange
        $messageData = ['from' => '5511999999999'];
        $conversation = $this->createMock(WhatsAppConversation::class);
        $interaction = $this->createMock(WhatsAppInteraction::class);

        $this->mockInteractionFactory
            ->expects($this->once())
            ->method('buildFromWebhookData')
            ->willReturn($interaction);

        $this->mockInteractionRepository
            ->expects($this->once())
            ->method('save')
            ->willThrowException(new Exception('Database save failed'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database save failed');

        $this->processFlowStep->perform($conversation, $messageData);
    }

    public function test_perform_applies_conditional_navigation()
    {
        // Arrange
        $messageData = ['from' => '5511999999999'];

        $step = $this->createMock(Step::class);
        $step->id = 6;
        $step->type = 'message';
        $step->message = 'Test message';

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->current_step = $step;

        $interaction = $this->createMock(WhatsAppInteraction::class);
        $savedInteraction = $this->createMock(WhatsAppInteraction::class);

        $originalResult = [
            'type' => 'message',
            'step_id' => 6,
            'action' => 'send_message',
            'move_to_next' => true
        ];

        $modifiedResult = [
            'type' => 'message',
            'step_id' => 6,
            'action' => 'send_message',
            'move_to_next' => true,
            'conditional_next_step' => 10 // Modified by conditional navigation
        ];

        $this->mockInteractionFactory
            ->expects($this->once())
            ->method('buildFromWebhookData')
            ->willReturn($interaction);

        $this->mockInteractionRepository
            ->expects($this->exactly(2))
            ->method('save')
            ->willReturn($savedInteraction);

        $this->mockConditionalNavigationService
            ->expects($this->once())
            ->method('hasConditionalNavigation')
            ->with($originalResult, $step, $interaction)
            ->willReturn($modifiedResult);

        
        // Mock step processor factory
        $mockProcessor = $this->createMock(\App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface::class);
        $mockProcessor->method('process')->willReturn([
            'type' => 'test',
            'step_id' => 1,
            'action' => 'test_action',
            'message' => 'Test message',
            'move_to_next' => true,
            'next_step' => 2
        ]);
        
        $this->mockStepProcessorFactory
            ->method('getProcessor')
            ->willReturn($mockProcessor);
        
        // Act
        $result = $this->processFlowStep->perform($conversation, $messageData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals(10, $result['conditional_next_step']);
    }

    public function test_perform_handles_dynamic_input_service_exception()
    {
        // Arrange
        $messageData = ['from' => '5511999999999'];

        $step = $this->createMock(Step::class);
        $step->id = 7;
        $step->type = 'input';
        $step->is_input = true;

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->current_step = $step;

        $interaction = $this->createMock(WhatsAppInteraction::class);
        $savedInteraction = $this->createMock(WhatsAppInteraction::class);

        $this->mockInteractionFactory
            ->expects($this->once())
            ->method('buildFromWebhookData')
            ->willReturn($interaction);

        $this->mockInteractionRepository
            ->expects($this->once())
            ->method('save')
            ->willReturn($savedInteraction);

        $this->mockDynamicInputService
            ->expects($this->once())
            ->method('processInput')
            ->willThrowException(new Exception('Input processing failed'));

        
        // Mock step processor factory
        $mockProcessor = $this->createMock(\App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface::class);
        $mockProcessor->method('process')->willReturn([
            'type' => 'test',
            'step_id' => 1,
            'action' => 'test_action',
            'message' => 'Test message',
            'move_to_next' => true,
            'next_step' => 2
        ]);
        
        $this->mockStepProcessorFactory
            ->method('getProcessor')
            ->willReturn($mockProcessor);
        
        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Input processing failed');

        $this->processFlowStep->perform($conversation, $messageData);
    }
}
