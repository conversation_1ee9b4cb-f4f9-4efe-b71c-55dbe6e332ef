<?php

namespace Tests\Unit\Services\Meta\WhatsApp\Builders;

use App\Domains\ChatBot\Button;
use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\ListRow;
use App\Domains\ChatBot\ListSection;
use App\Domains\ChatBot\Template;
use App\Enums\ChatBot\ComponentFormat;
use App\Enums\ChatBot\ComponentType;
use App\Services\Meta\WhatsApp\Builders\WhatsAppMessageBuilder;
use InvalidArgumentException;
use Tests\TestCase;

class WhatsAppMessageBuilderTest extends TestCase
{
    private WhatsAppMessageBuilder $builder;

    protected function setUp(): void
    {
        parent::setUp();
        $this->builder = new WhatsAppMessageBuilder();
    }

    public function test_build_text_message_success()
    {
        $to = '+5579991234567';
        $text = 'Hello, this is a test message!';

        $payload = $this->builder->buildTextMessage($to, $text);

        $this->assertEquals([
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'text',
            'text' => [
                'body' => $text,
                'preview_url' => false
            ]
        ], $payload);
    }

    public function test_build_text_message_with_preview_url()
    {
        $to = '+5579991234567';
        $text = 'Check out this link: https://example.com';

        $payload = $this->builder->buildTextMessage($to, $text, true);

        $this->assertEquals([
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'text',
            'text' => [
                'body' => $text,
                'preview_url' => true
            ]
        ], $payload);
    }

    public function test_build_text_message_validates_phone_number()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Phone number is required');

        $this->builder->buildTextMessage('', 'Test message');
    }

    public function test_build_text_message_validates_text_length()
    {
        $longText = str_repeat('a', 4097); // Exceeds MAX_TEXT_LENGTH

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Message text exceeds maximum length');

        $this->builder->buildTextMessage('+5579991234567', $longText);
    }

    public function test_build_interactive_button_message_success()
    {
        $to = '+5579991234567';
        $body = 'Choose an option:';
        $buttons = [
            new Button(1, 1, 'Yes', 'reply', null, null, null, null),
            new Button(2, 1, 'No', 'reply', null, null, null, null)
        ];

        $payload = $this->builder->buildInteractiveButtonMessage($to, $body, $buttons);

        $this->assertEquals('whatsapp', $payload['messaging_product']);
        $this->assertEquals($to, $payload['to']);
        $this->assertEquals('interactive', $payload['type']);
        $this->assertEquals('button', $payload['interactive']['type']);
        $this->assertEquals($body, $payload['interactive']['body']['text']);
        $this->assertCount(2, $payload['interactive']['action']['buttons']);
    }

    public function test_build_interactive_button_message_with_header_and_footer()
    {
        $to = '+5579991234567';
        $body = 'Choose an option:';
        $header = 'Important Question';
        $footer = 'Please select one';
        $buttons = [
            new Button(1, 1, 'Yes', 'reply', null, null, null, null)
        ];

        $payload = $this->builder->buildInteractiveButtonMessage($to, $body, $buttons, $header, $footer);

        $this->assertEquals($header, $payload['interactive']['header']['text']);
        $this->assertEquals($footer, $payload['interactive']['footer']['text']);
    }

    public function test_build_interactive_button_message_validates_max_buttons()
    {
        $to = '+5579991234567';
        $body = 'Choose an option:';
        $buttons = [
            new Button(1, 1, 'Option 1', 'reply', null, null, null, null),
            new Button(2, 1, 'Option 2', 'reply', null, null, null, null),
            new Button(3, 1, 'Option 3', 'reply', null, null, null, null),
            new Button(4, 1, 'Option 4', 'reply', null, null, null, null) // Exceeds max of 3
        ];

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Maximum of 3 buttons allowed');

        $this->builder->buildInteractiveButtonMessage($to, $body, $buttons);
    }

    public function test_build_interactive_list_message_success()
    {
        $to = '+5579991234567';
        $body = 'Select from the list:';
        $sections = [
            new ListSection(
                1,
                1,
                'Options',
                null,
                null,
                [
                    new ListRow(1, 1, 'option1', 'Option 1', 'First option'),
                    new ListRow(2, 1, 'option2', 'Option 2', 'Second option')
                ]
            )
        ];

        $payload = $this->builder->buildInteractiveListMessage($to, $body, $sections);

        $this->assertEquals('whatsapp', $payload['messaging_product']);
        $this->assertEquals($to, $payload['to']);
        $this->assertEquals('interactive', $payload['type']);
        $this->assertEquals('list', $payload['interactive']['type']);
        $this->assertEquals($body, $payload['interactive']['body']['text']);
        $this->assertEquals('Ver opções', $payload['interactive']['action']['button']);
        $this->assertCount(1, $payload['interactive']['action']['sections']);
    }

    public function test_build_interactive_list_message_with_custom_button_text()
    {
        $to = '+5579991234567';
        $body = 'Select from the list:';
        $buttonText = 'Choose Option';
        $sections = [
            new ListSection(1, 1, 'Options', null, null, [
                new ListRow(1, 1, 'option1', 'Option 1', 'First option')
            ])
        ];

        $payload = $this->builder->buildInteractiveListMessage($to, $body, $sections, $buttonText);

        $this->assertEquals($buttonText, $payload['interactive']['action']['button']);
    }

    public function test_build_template_message_success()
    {
        $to = '+5579991234567';
        $template = new Template(
            id: 1,
            organization_id: 1,
            phone_number_id: 1,
            user_id: 1,
            client_id: null,
            name: 'welcome_template',
            category: 'MARKETING',
            parameter_format: null,
            language: 'pt_BR',
            library_template_name: null,
            id_external: null,
            status: 'APPROVED',
            created_at: null,
            updated_at: null,
            is_whatsapp_published: true
        );

        $payload = $this->builder->buildTemplateMessage($to, $template);

        $this->assertEquals([
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'template',
            'template' => [
                'name' => 'welcome_template',
                'language' => [
                    'code' => 'pt_BR'
                ]
            ]
        ], $payload);
    }

    public function test_build_template_message_validates_published_status()
    {
        $to = '+5579991234567';
        $template = new Template(
            id: 1,
            organization_id: 1,
            phone_number_id: 1,
            user_id: 1,
            client_id: null,
            name: 'unpublished_template',
            category: 'MARKETING',
            parameter_format: null,
            language: 'pt_BR',
            library_template_name: null,
            id_external: null,
            status: 'PENDING',
            created_at: null,
            updated_at: null,
            is_whatsapp_published: false
        );

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Template must be published on WhatsApp');

        $this->builder->buildTemplateMessage($to, $template);
    }

    public function test_validate_payload_text_message()
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => '+5579991234567',
            'type' => 'text',
            'text' => [
                'body' => 'Test message'
            ]
        ];

        $this->assertTrue($this->builder->validatePayload($payload));
    }

    public function test_validate_payload_missing_required_field()
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'type' => 'text',
            'text' => [
                'body' => 'Test message'
            ]
        ];

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Required field 'to' is missing from payload");

        $this->builder->validatePayload($payload);
    }

    public function test_validate_payload_invalid_messaging_product()
    {
        $payload = [
            'messaging_product' => 'telegram',
            'to' => '+5579991234567',
            'type' => 'text',
            'text' => [
                'body' => 'Test message'
            ]
        ];

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('messaging_product must be "whatsapp"');

        $this->builder->validatePayload($payload);
    }

    public function test_validate_payload_unsupported_message_type()
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => '+5579991234567',
            'type' => 'unsupported',
            'content' => 'Test'
        ];

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Unsupported message type: unsupported');

        $this->builder->validatePayload($payload);
    }

    public function test_phone_number_validation_too_short()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Phone number must be between 10 and 15 digits');

        $this->builder->buildTextMessage('123456789', 'Test message'); // 9 digits
    }

    public function test_phone_number_validation_too_long()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Phone number must be between 10 and 15 digits');

        $this->builder->buildTextMessage('1234567890123456', 'Test message'); // 16 digits
    }

    public function test_template_name_validation_invalid_format()
    {
        $to = '+5579991234567';
        $template = new Template(
            id: 1,
            organization_id: 1,
            phone_number_id: 1,
            user_id: 1,
            client_id: null,
            name: 'Invalid-Template-Name!',
            category: 'MARKETING',
            parameter_format: null,
            language: 'pt_BR',
            library_template_name: null,
            id_external: null,
            status: 'APPROVED',
            created_at: null,
            updated_at: null,
            is_whatsapp_published: true
        );

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Template name must contain only lowercase letters, numbers, and underscores');

        $this->builder->buildTemplateMessage($to, $template);
    }

    public function test_template_language_validation_invalid_format()
    {
        $to = '+5579991234567';
        $template = new Template(
            id: 1,
            organization_id: 1,
            phone_number_id: 1,
            user_id: 1,
            client_id: null,
            name: 'valid_template',
            category: 'MARKETING',
            parameter_format: null,
            language: 'invalid_language_code',
            library_template_name: null,
            id_external: null,
            status: 'APPROVED',
            created_at: null,
            updated_at: null,
            is_whatsapp_published: true
        );

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Template language must be a valid language code');

        $this->builder->buildTemplateMessage($to, $template);
    }

    public function test_validate_interactive_button_payload_structure()
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => '+5579991234567',
            'type' => 'interactive',
            'interactive' => [
                'type' => 'button',
                'body' => [
                    'text' => 'Choose an option'
                ],
                'action' => [
                    'buttons' => [
                        [
                            'type' => 'reply',
                            'reply' => [
                                'id' => 'btn1',
                                'title' => 'Option 1'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $this->assertTrue($this->builder->validatePayload($payload));
    }

    public function test_validate_interactive_list_payload_structure()
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => '+5579991234567',
            'type' => 'interactive',
            'interactive' => [
                'type' => 'list',
                'body' => [
                    'text' => 'Select from list'
                ],
                'action' => [
                    'button' => 'View Options',
                    'sections' => [
                        [
                            'title' => 'Section 1',
                            'rows' => [
                                [
                                    'id' => 'row1',
                                    'title' => 'Row 1',
                                    'description' => 'Description 1'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $this->assertTrue($this->builder->validatePayload($payload));
    }

    public function test_validate_template_payload_structure()
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => '+5579991234567',
            'type' => 'template',
            'template' => [
                'name' => 'welcome_template',
                'language' => [
                    'code' => 'pt_BR'
                ]
            ]
        ];

        $this->assertTrue($this->builder->validatePayload($payload));
    }

    public function test_empty_text_validation()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Message text cannot be empty');

        $this->builder->buildTextMessage('+5579991234567', '');
    }

    public function test_empty_body_validation_interactive()
    {
        $buttons = [new Button(1, 1, 'Yes', 'reply', null, null, null, null)];

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Message body cannot be empty');

        $this->builder->buildInteractiveButtonMessage('+5579991234567', '', $buttons);
    }

    public function test_no_buttons_validation()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('At least one button is required');

        $this->builder->buildInteractiveButtonMessage('+5579991234567', 'Choose option', []);
    }

    public function test_no_sections_validation()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('At least one section is required');

        $this->builder->buildInteractiveListMessage('+5579991234567', 'Select option', []);
    }

    public function test_header_length_validation()
    {
        $longHeader = str_repeat('a', 61); // Exceeds MAX_HEADER_LENGTH
        $buttons = [new Button(1, 1, 'Yes', 'reply', null, null, null, null)];

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Header exceeds maximum length');

        $this->builder->buildInteractiveButtonMessage('+5579991234567', 'Body', $buttons, $longHeader);
    }

    public function test_footer_length_validation()
    {
        $longFooter = str_repeat('a', 61); // Exceeds MAX_FOOTER_LENGTH
        $buttons = [new Button(1, 1, 'Yes', 'reply', null, null, null, null)];

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Footer exceeds maximum length');

        $this->builder->buildInteractiveButtonMessage('+5579991234567', 'Body', $buttons, null, $longFooter);
    }

    public function test_button_text_length_validation()
    {
        $longButtonText = str_repeat('a', 21); // Exceeds MAX_LIST_BUTTON_TEXT_LENGTH
        $sections = [
            new ListSection(1, 1, 'Options', null, null, [
                new ListRow(1, 1, 'option1', 'Option 1', 'Description')
            ])
        ];

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Button text exceeds maximum length');

        $this->builder->buildInteractiveListMessage('+5579991234567', 'Body', $sections, $longButtonText);
    }

    public function test_app_make_instantiation()
    {
        $builder = app()->make(WhatsAppMessageBuilder::class);

        $this->assertInstanceOf(WhatsAppMessageBuilder::class, $builder);

        // Test that it can build a simple message
        $payload = $builder->buildTextMessage('+5579991234567', 'Test message');
        $this->assertEquals('whatsapp', $payload['messaging_product']);
    }
}
