<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\ListSection;
use App\Domains\ChatBot\ListRow;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;
use InvalidArgumentException;

class ListSectionTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(ListSection::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->interactive_message_id);
        $this->assertEquals('Test Section', $domain->title);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
    }

    public function test_section_with_valid_rows()
    {
        $rows = [
            new ListRow(1, 1, 'row_1', 'Option 1', 'Description 1'),
            new ListRow(2, 1, 'row_2', 'Option 2', 'Description 2'),
        ];

        $section = new ListSection(
            id: 1,
            interactive_message_id: 1,
            title: 'Test Section',
            rows: $rows
        );

        $this->assertTrue($section->isValid());
        $this->assertEmpty($section->getValidationErrors());
        $this->assertEquals(2, $section->getRowCount());
    }

    public function test_section_validation_fails_with_empty_rows()
    {
        $section = new ListSection(
            id: 1,
            interactive_message_id: 1,
            title: 'Test Section',
            rows: []
        );

        $this->assertFalse($section->isValid());
        $errors = $section->getValidationErrors();
        $this->assertContains('Section must have at least 1 row', $errors);
    }

    public function test_section_validation_fails_with_invalid_row_type()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('All rows must be ListRow instances');

        new ListSection(
            id: 1,
            interactive_message_id: 1,
            title: 'Test Section',
            rows: ['invalid_row']
        );
    }

    public function test_add_row_to_section()
    {
        $section = new ListSection(
            id: 1,
            interactive_message_id: 1,
            title: 'Test Section'
        );

        $row = new ListRow(1, 1, 'row_1', 'Option 1', 'Description 1');
        $section->addRow($row);

        $this->assertEquals(1, $section->getRowCount());
        $this->assertEquals('Option 1', $section->rows[0]->title);
    }

    public function test_remove_row_from_section()
    {
        $rows = [
            new ListRow(1, 1, 'row_1', 'Option 1', 'Description 1'),
            new ListRow(2, 1, 'row_2', 'Option 2', 'Description 2'),
        ];

        $section = new ListSection(
            id: 1,
            interactive_message_id: 1,
            title: 'Test Section',
            rows: $rows
        );

        $this->assertEquals(2, $section->getRowCount());

        $section->removeRow(0);

        $this->assertEquals(1, $section->getRowCount());
        $this->assertEquals('Option 2', $section->rows[0]->title);
    }

    public function test_remove_row_fails_with_invalid_index()
    {
        $section = new ListSection(
            id: 1,
            interactive_message_id: 1,
            title: 'Test Section'
        );

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Row index does not exist');

        $section->removeRow(0);
    }

    public function test_whatsapp_payload_generation()
    {
        $rows = [
            new ListRow(1, 1, 'option_1', 'Option 1', 'First option'),
            new ListRow(2, 1, 'option_2', 'Option 2', 'Second option'),
        ];

        $section = new ListSection(
            id: 1,
            interactive_message_id: 1,
            title: 'Choose Option',
            rows: $rows
        );

        $payload = $section->toWhatsAppPayload();

        $this->assertEquals('Choose Option', $payload['title']);
        $this->assertCount(2, $payload['rows']);
        $this->assertEquals('option_1', $payload['rows'][0]['id']);
        $this->assertEquals('Option 1', $payload['rows'][0]['title']);
        $this->assertEquals('First option', $payload['rows'][0]['description']);
    }

    public function test_whatsapp_payload_generation_without_title()
    {
        $rows = [
            new ListRow(1, 1, 'option_1', 'Option 1', 'First option'),
        ];

        $section = new ListSection(
            id: 1,
            interactive_message_id: 1,
            title: null,
            rows: $rows
        );

        $payload = $section->toWhatsAppPayload();

        $this->assertArrayNotHasKey('title', $payload);
        $this->assertCount(1, $payload['rows']);
    }

    public function test_validation_errors_for_empty_section()
    {
        $section = new ListSection(
            id: 1,
            interactive_message_id: 1,
            title: 'Test Section'
        );

        $this->assertFalse($section->isValid());
        $errors = $section->getValidationErrors();
        $this->assertContains('Section must have at least 1 row', $errors);
    }

    public function test_to_store_array()
    {
        $section = new ListSection(
            id: 1,
            interactive_message_id: 1,
            title: 'Test Section'
        );

        $storeArray = $section->toStoreArray();

        $this->assertEquals(1, $storeArray['interactive_message_id']);
        $this->assertEquals('Test Section', $storeArray['title']);
        $this->assertArrayNotHasKey('id', $storeArray);
    }

    public function test_to_update_array()
    {
        $section = new ListSection(
            id: 1,
            interactive_message_id: 1,
            title: 'Test Section'
        );

        $updateArray = $section->toUpdateArray();

        $this->assertEquals('Test Section', $updateArray['title']);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('interactive_message_id', $updateArray);
    }

    protected function createDomainInstance()
    {
        return new ListSection(
            id: 1,
            interactive_message_id: 1,
            title: 'Test Section',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'interactive_message_id',
            'title',
            'created_at',
            'updated_at',
            'rows',
            'row_count',
            'is_valid',
        ];
    }
}
