<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\InteractiveMessage;
use App\Domains\ChatBot\Button;
use App\Domains\ChatBot\ListSection;
use App\Domains\ChatBot\ListRow;
use App\Enums\ChatBot\InteractiveType;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;
use InvalidArgumentException;

class InteractiveMessageTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(InteractiveMessage::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Test Header', $domain->header);
        $this->assertEquals('Test Body', $domain->body);
        $this->assertEquals('Test Footer', $domain->footer);
        $this->assertEquals(InteractiveType::BUTTON, $domain->type);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
    }

    public function test_button_message_validation_success()
    {
        $buttons = [
            new Button(1, 1, 'Button 1', 'reply', null, null, null, null),
            new Button(2, 1, 'Button 2', 'reply', null, null, null, null),
            new Button(3, 1, 'Button 3', 'reply', null, null, null, null),
        ];

        $interactiveMessage = new InteractiveMessage(
            id: 1,
            organization_id: 1,
            header: 'Test Header',
            body: 'Test Body',
            footer: 'Test Footer',
            type: InteractiveType::BUTTON,
            buttons: $buttons
        );

        $this->assertTrue($interactiveMessage->isValid());
        $this->assertEmpty($interactiveMessage->getValidationErrors());
    }

    public function test_button_message_validation_fails_with_more_than_3_buttons()
    {
        $buttons = [
            new Button(1, 1, 'Button 1', 'reply', null, null, null, null),
            new Button(2, 1, 'Button 2', 'reply', null, null, null, null),
            new Button(3, 1, 'Button 3', 'reply', null, null, null, null),
            new Button(4, 1, 'Button 4', 'reply', null, null, null, null),
        ];

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Button messages support maximum 3 buttons');

        new InteractiveMessage(
            id: 1,
            organization_id: 1,
            body: 'Test Body',
            type: InteractiveType::BUTTON,
            buttons: $buttons
        );
    }

    public function test_list_message_validation_success()
    {
        $rows = [
            new ListRow(1, 1, 'row_1', 'Option 1', 'Description 1'),
            new ListRow(2, 1, 'row_2', 'Option 2', 'Description 2'),
        ];

        $sections = [
            new ListSection(1, 1, 'Section 1', null, null, $rows),
        ];

        $interactiveMessage = new InteractiveMessage(
            id: 1,
            organization_id: 1,
            body: 'Test Body',
            type: InteractiveType::LIST,
            button_text: 'Ver opções',
            sections: $sections
        );

        $this->assertTrue($interactiveMessage->isValid());
        $this->assertEmpty($interactiveMessage->getValidationErrors());
        $this->assertEquals(2, $interactiveMessage->getTotalRows());
    }

    public function test_list_message_validation_fails_with_more_than_10_rows()
    {
        $rows = [];
        for ($i = 1; $i <= 11; $i++) {
            $rows[] = new ListRow($i, 1, "row_$i", "Option $i", "Description $i");
        }

        $sections = [
            new ListSection(1, 1, 'Section 1', null, null, $rows),
        ];

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('List messages support maximum 10 options total');

        new InteractiveMessage(
            id: 1,
            organization_id: 1,
            body: 'Test Body',
            type: InteractiveType::LIST,
            sections: $sections
        );
    }

    public function test_add_button_to_button_message()
    {
        $interactiveMessage = new InteractiveMessage(
            id: 1,
            organization_id: 1,
            body: 'Test Body',
            type: InteractiveType::BUTTON
        );

        $button = new Button(1, 1, 'Test Button', 'reply', null, null, null, null);
        $interactiveMessage->addButton($button);

        $this->assertCount(1, $interactiveMessage->buttons);
        $this->assertEquals('Test Button', $interactiveMessage->buttons[0]->text);
    }

    public function test_add_button_fails_on_list_message()
    {
        $interactiveMessage = new InteractiveMessage(
            id: 1,
            organization_id: 1,
            body: 'Test Body',
            type: InteractiveType::LIST
        );

        $button = new Button(1, 1, 'Test Button', 'reply', null, null, null, null);

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Can only add buttons to BUTTON type interactive messages');

        $interactiveMessage->addButton($button);
    }

    public function test_add_section_to_list_message()
    {
        $interactiveMessage = new InteractiveMessage(
            id: 1,
            organization_id: 1,
            body: 'Test Body',
            type: InteractiveType::LIST
        );

        $rows = [
            new ListRow(1, 1, 'row_1', 'Option 1', 'Description 1'),
        ];
        $section = new ListSection(1, 1, 'Section 1', null, null, $rows);
        $interactiveMessage->addSection($section);

        $this->assertCount(1, $interactiveMessage->sections);
        $this->assertEquals('Section 1', $interactiveMessage->sections[0]->title);
        $this->assertEquals(1, $interactiveMessage->getTotalRows());
    }

    public function test_add_section_fails_on_button_message()
    {
        $interactiveMessage = new InteractiveMessage(
            id: 1,
            organization_id: 1,
            body: 'Test Body',
            type: InteractiveType::BUTTON
        );

        $section = new ListSection(1, 1, 'Section 1');

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Can only add sections to LIST type interactive messages');

        $interactiveMessage->addSection($section);
    }

    public function test_whatsapp_payload_generation_for_button_message()
    {
        $buttons = [
            new Button(1, 1, 'Yes', 'reply', null, null, null, null),
            new Button(2, 1, 'No', 'reply', null, null, null, null),
        ];

        $interactiveMessage = new InteractiveMessage(
            id: 1,
            organization_id: 1,
            header: 'Confirm Action',
            body: 'Do you want to proceed?',
            footer: 'Choose an option',
            type: InteractiveType::BUTTON,
            buttons: $buttons
        );

        $payload = $interactiveMessage->toWhatsAppPayload('+5511999999999');

        $this->assertEquals('interactive', $payload['type']);
        $this->assertEquals('+5511999999999', $payload['to']);
        $this->assertEquals('button', $payload['interactive']['type']);
        $this->assertEquals('Do you want to proceed?', $payload['interactive']['body']['text']);
        $this->assertEquals('Confirm Action', $payload['interactive']['header']['text']);
        $this->assertEquals('Choose an option', $payload['interactive']['footer']['text']);
        $this->assertCount(2, $payload['interactive']['action']['buttons']);
    }

    public function test_whatsapp_payload_generation_for_list_message()
    {
        $rows = [
            new ListRow(1, 1, 'option_1', 'Option 1', 'First option'),
            new ListRow(2, 1, 'option_2', 'Option 2', 'Second option'),
        ];

        $sections = [
            new ListSection(1, 1, 'Choose Option', null, null, $rows),
        ];

        $interactiveMessage = new InteractiveMessage(
            id: 1,
            organization_id: 1,
            body: 'Please select an option',
            type: InteractiveType::LIST,
            button_text: 'View Options',
            sections: $sections
        );

        $payload = $interactiveMessage->toWhatsAppPayload('+5511999999999');

        $this->assertEquals('interactive', $payload['type']);
        $this->assertEquals('+5511999999999', $payload['to']);
        $this->assertEquals('list', $payload['interactive']['type']);
        $this->assertEquals('Please select an option', $payload['interactive']['body']['text']);
        $this->assertEquals('View Options', $payload['interactive']['action']['button']);
        $this->assertCount(1, $payload['interactive']['action']['sections']);
    }

    protected function createDomainInstance()
    {
        return new InteractiveMessage(
            id: 1,
            organization_id: 1,
            header: 'Test Header',
            body: 'Test Body',
            footer: 'Test Footer',
            type: InteractiveType::BUTTON,
            button_text: 'Ver opções',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'header',
            'body',
            'footer',
            'type',
            'button_text',
            'created_at',
            'updated_at',
            'buttons',
            'sections',
            'total_rows',
            'is_valid',
        ];
    }
}
