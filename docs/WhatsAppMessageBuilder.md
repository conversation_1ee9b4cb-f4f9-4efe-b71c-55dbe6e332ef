# WhatsApp Message Builder

O `WhatsAppMessageBuilder` é uma classe especializada para construir payloads de mensagens WhatsApp conformes com a API oficial. Fornece métodos específicos para diferentes tipos de mensagens com validações robustas.

## Instalação e Uso Básico

```php
use App\Services\Meta\WhatsApp\Builders\WhatsAppMessageBuilder;

// Instanciar o builder
$builder = new WhatsAppMessageBuilder();

// Ou usar dependency injection
$builder = app()->make(WhatsAppMessageBuilder::class);
```

## Tipos de Mensagem Suportados

### 1. Mensagens de Texto Simples

Cria mensagens de texto básicas com suporte a preview de URL.

```php
// Mensagem de texto simples
$payload = $builder->buildTextMessage(
    to: '+5579991234567',
    text: 'Olá! Esta é uma mensagem de texto simples.'
);

// Mensagem de texto com preview de URL
$payload = $builder->buildTextMessage(
    to: '+5579991234567',
    text: 'Confira nosso site: https://exemplo.com',
    previewUrl: true
);
```

**Resultado:**
```json
{
    "messaging_product": "whatsapp",
    "to": "+5579991234567",
    "type": "text",
    "text": {
        "body": "Olá! Esta é uma mensagem de texto simples.",
        "preview_url": false
    }
}
```

### 2. Mensagens Interativas com Botões

Cria mensagens com até 3 botões de resposta rápida.

```php
use App\Domains\ChatBot\Button;

// Criar botões
$buttons = [
    new Button(1, 1, 'Sim', 'reply', null, null, null, null),
    new Button(2, 1, 'Não', 'reply', null, null, null, null),
    new Button(3, 1, 'Talvez', 'reply', null, null, null, null)
];

// Mensagem com botões (básica)
$payload = $builder->buildInteractiveButtonMessage(
    to: '+5579991234567',
    body: 'Você gostaria de receber nossas novidades?',
    buttons: $buttons
);

// Mensagem com botões (completa com header e footer)
$payload = $builder->buildInteractiveButtonMessage(
    to: '+5579991234567',
    body: 'Você gostaria de receber nossas novidades?',
    buttons: $buttons,
    header: 'Newsletter',
    footer: 'Escolha uma opção'
);
```

**Resultado:**
```json
{
    "messaging_product": "whatsapp",
    "to": "+5579991234567",
    "type": "interactive",
    "interactive": {
        "type": "button",
        "header": {
            "type": "text",
            "text": "Newsletter"
        },
        "body": {
            "text": "Você gostaria de receber nossas novidades?"
        },
        "footer": {
            "text": "Escolha uma opção"
        },
        "action": {
            "buttons": [
                {
                    "type": "reply",
                    "reply": {
                        "id": "1",
                        "title": "Sim"
                    }
                }
            ]
        }
    }
}
```

### 3. Mensagens Interativas com Lista

Cria mensagens com listas de opções selecionáveis.

```php
use App\Domains\ChatBot\ListSection;
use App\Domains\ChatBot\ListRow;

// Criar seções e linhas
$sections = [
    new ListSection(
        id: 1,
        interactive_message_id: 1,
        title: 'Produtos',
        created_at: null,
        updated_at: null,
        rows: [
            new ListRow(1, 1, 'produto_1', 'Smartphone', 'Últimos modelos disponíveis'),
            new ListRow(2, 1, 'produto_2', 'Notebook', 'Para trabalho e estudos'),
            new ListRow(3, 1, 'produto_3', 'Tablet', 'Portabilidade e praticidade')
        ]
    ),
    new ListSection(
        id: 2,
        interactive_message_id: 1,
        title: 'Serviços',
        created_at: null,
        updated_at: null,
        rows: [
            new ListRow(4, 1, 'servico_1', 'Suporte Técnico', 'Assistência especializada'),
            new ListRow(5, 1, 'servico_2', 'Garantia Estendida', 'Proteção adicional')
        ]
    )
];

// Mensagem com lista (básica)
$payload = $builder->buildInteractiveListMessage(
    to: '+5579991234567',
    body: 'Selecione uma categoria:',
    sections: $sections
);

// Mensagem com lista (personalizada)
$payload = $builder->buildInteractiveListMessage(
    to: '+5579991234567',
    body: 'Selecione uma categoria:',
    sections: $sections,
    buttonText: 'Ver Categorias',
    header: 'Catálogo',
    footer: 'Escolha uma opção'
);
```

**Resultado:**
```json
{
    "messaging_product": "whatsapp",
    "to": "+5579991234567",
    "type": "interactive",
    "interactive": {
        "type": "list",
        "header": {
            "type": "text",
            "text": "Catálogo"
        },
        "body": {
            "text": "Selecione uma categoria:"
        },
        "footer": {
            "text": "Escolha uma opção"
        },
        "action": {
            "button": "Ver Categorias",
            "sections": [
                {
                    "title": "Produtos",
                    "rows": [
                        {
                            "id": "produto_1",
                            "title": "Smartphone",
                            "description": "Últimos modelos disponíveis"
                        }
                    ]
                }
            ]
        }
    }
}
```

### 4. Mensagens de Template

Cria mensagens usando templates aprovados pelo WhatsApp.

```php
use App\Domains\ChatBot\Template;

// Criar template
$template = new Template(
    id: 1,
    organization_id: 1,
    phone_number_id: 1,
    user_id: 1,
    client_id: null,
    name: 'welcome_message',
    category: 'MARKETING',
    parameter_format: null,
    language: 'pt_BR',
    library_template_name: null,
    id_external: 'template_123',
    status: 'APPROVED',
    created_at: null,
    updated_at: null,
    is_whatsapp_published: true
);

// Mensagem de template
$payload = $builder->buildTemplateMessage(
    to: '+5579991234567',
    template: $template
);

// Mensagem de template com variáveis
$availableModels = [
    'client' => (object) ['name' => 'João Silva', 'email' => '<EMAIL>']
];

$payload = $builder->buildTemplateMessage(
    to: '+5579991234567',
    template: $template,
    availableModels: $availableModels
);
```

**Resultado:**
```json
{
    "messaging_product": "whatsapp",
    "to": "+5579991234567",
    "type": "template",
    "template": {
        "name": "welcome_message",
        "language": {
            "code": "pt_BR"
        },
        "components": [
            {
                "type": "body",
                "parameters": [
                    {
                        "type": "text",
                        "text": "João Silva"
                    }
                ]
            }
        ]
    }
}
```

## Validação de Payloads

O builder inclui um método para validar payloads completos:

```php
$payload = $builder->buildTextMessage('+5579991234567', 'Teste');

// Validar payload
try {
    $isValid = $builder->validatePayload($payload);
    echo "Payload válido!";
} catch (InvalidArgumentException $e) {
    echo "Erro de validação: " . $e->getMessage();
}
```

## Limites e Validações

### Limites de Texto
- **Texto da mensagem**: 4.096 caracteres
- **Header**: 60 caracteres
- **Footer**: 60 caracteres
- **Texto do botão**: 20 caracteres
- **Texto do botão da lista**: 20 caracteres

### Limites de Elementos
- **Botões interativos**: Máximo 3 botões
- **Seções de lista**: Máximo 10 seções
- **Linhas por seção**: Máximo 10 linhas
- **Total de linhas**: Máximo 10 linhas em todas as seções

### Validações de Formato
- **Número de telefone**: Entre 10 e 15 dígitos
- **Nome do template**: Apenas letras minúsculas, números e underscores
- **Código de idioma**: Formato ISO 639-1 (ex: pt_BR, en, es)

## Tratamento de Erros

Todos os métodos lançam `InvalidArgumentException` em caso de dados inválidos:

```php
try {
    $payload = $builder->buildTextMessage('', 'Mensagem'); // Número vazio
} catch (InvalidArgumentException $e) {
    echo $e->getMessage(); // "Phone number is required"
}

try {
    $longText = str_repeat('a', 5000);
    $payload = $builder->buildTextMessage('+5579991234567', $longText);
} catch (InvalidArgumentException $e) {
    echo $e->getMessage(); // "Message text exceeds maximum length of 4096 characters"
}
```

## Exemplos Práticos

### Fluxo de Atendimento Completo

```php
// 1. Mensagem de boas-vindas
$welcomePayload = $builder->buildTextMessage(
    '+5579991234567',
    'Olá! Bem-vindo ao nosso atendimento. Como posso ajudá-lo hoje?'
);

// 2. Menu de opções
$menuButtons = [
    new Button(1, 1, 'Vendas', 'reply', null, null, null, null),
    new Button(2, 1, 'Suporte', 'reply', null, null, null, null),
    new Button(3, 1, 'Financeiro', 'reply', null, null, null, null)
];

$menuPayload = $builder->buildInteractiveButtonMessage(
    '+5579991234567',
    'Selecione o departamento desejado:',
    $menuButtons,
    'Atendimento',
    'Escolha uma opção'
);

// 3. Lista de produtos
$productSections = [
    new ListSection(1, 1, 'Eletrônicos', null, null, [
        new ListRow(1, 1, 'smartphone', 'Smartphones', 'Últimos lançamentos'),
        new ListRow(2, 1, 'notebook', 'Notebooks', 'Para trabalho e estudo')
    ])
];

$catalogPayload = $builder->buildInteractiveListMessage(
    '+5579991234567',
    'Confira nossos produtos:',
    $productSections,
    'Ver Catálogo'
);
```

### Integração com Serviços

```php
use App\Services\Meta\WhatsApp\MessageService;

class CustomerService
{
    private WhatsAppMessageBuilder $builder;
    private MessageService $messageService;

    public function __construct(
        WhatsAppMessageBuilder $builder,
        MessageService $messageService
    ) {
        $this->builder = $builder;
        $this->messageService = $messageService;
    }

    public function sendWelcomeMessage(string $phoneNumber): void
    {
        $payload = $this->builder->buildTextMessage(
            $phoneNumber,
            'Obrigado por entrar em contato! Em que posso ajudá-lo?'
        );

        $this->messageService->send($payload);
    }

    public function sendProductMenu(string $phoneNumber): void
    {
        $buttons = [
            new Button(1, 1, 'Ver Produtos', 'reply', null, null, null, null),
            new Button(2, 1, 'Falar com Vendedor', 'reply', null, null, null, null)
        ];

        $payload = $this->builder->buildInteractiveButtonMessage(
            $phoneNumber,
            'Como posso ajudá-lo com nossos produtos?',
            $buttons,
            'Vendas'
        );

        $this->messageService->send($payload);
    }
}
```

## Compliance com WhatsApp API

O `WhatsAppMessageBuilder` garante 100% de compliance com a API oficial do WhatsApp:

- ✅ Estrutura de payload correta
- ✅ Validações de limite de caracteres
- ✅ Validações de número de elementos
- ✅ Formato correto de números de telefone
- ✅ Validações de templates
- ✅ Tratamento de erros robusto

## Extensibilidade

A classe foi projetada para ser facilmente extensível. Para adicionar novos tipos de mensagem:

1. Adicione o método público `buildNewMessageType()`
2. Implemente validações específicas em métodos privados
3. Adicione testes unitários correspondentes
4. Atualize a documentação

---

**Nota**: Esta documentação cobre a versão atual do WhatsAppMessageBuilder. Para atualizações e novos recursos, consulte os testes unitários e o código fonte.
