<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('interactive_message_button', function (Blueprint $table) {
            $table->id();
            $table->foreignId('interactive_message_id')->constrained()->onDelete('cascade');
            $table->foreignId('button_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            // Unique constraint to prevent duplicate relationships
            $table->unique(['interactive_message_id', 'button_id']);

            // Indexes
            $table->index(['interactive_message_id']);
            $table->index(['button_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interactive_message_button');
    }
};
