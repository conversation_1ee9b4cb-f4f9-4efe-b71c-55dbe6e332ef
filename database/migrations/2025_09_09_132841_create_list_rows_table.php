<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('list_rows', function (Blueprint $table) {
            $table->id();
            $table->foreignId('list_section_id')->constrained()->onDelete('cascade');

            // Row identifier for WhatsApp API (max 200 characters)
            $table->string('row_id', 200);

            // Row title (max 24 characters for WhatsApp API)
            $table->string('title', 24);

            // Row description (max 72 characters for WhatsApp API)
            $table->string('description', 72)->nullable();

            $table->timestamps();

            // Indexes
            $table->index(['list_section_id']);
            $table->index(['list_section_id', 'row_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('list_rows');
    }
};
